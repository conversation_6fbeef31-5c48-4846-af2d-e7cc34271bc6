<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="9180" viewBox="0 0 1400 9180" xmlns="http://www.w3.org/2000/svg" style="display: block; margin: 0 auto; max-width: 100%; height: auto;">
  <defs>
    <!-- 漸層定義 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="authorGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="entityGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="qlistGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="faqGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#c471f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fa71cd;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="evidenceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    
    <!-- 陰影效果 -->
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#0000001A"/>
    </filter>
  </defs>

  <!-- 主標題區域 -->
  <g>
    <rect width="1400" height="200" fill="url(#headerGradient)" filter="url(#shadow)"/>
    <rect x="20" y="20" width="1360" height="160" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 主標題 -->
    <text x="700" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="36" font-weight="bold" fill="#2d3748">🎯 真相終點站(Source of Truth) 社群→AI 拉動 關鍵戰術 11-20</text>
    <text x="700" y="105" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="20" fill="#4a5568">讓人和 AI 都能秒懂、秒信、秒用你的內容</text>
    <text x="700" y="135" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="rgba(255,255,255,0.9)">SEO優化王 × 柏瀚國際 AISO 360™ 戰略框架</text>
    <text x="700" y="160" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="rgba(255,255,255,0.9)">從身份建立(E-E-A-T)到即時索引(Indexing)的完整鏈條</text>
  </g> 
 <!-- 框架總覽 -->
  <g transform="translate(50, 230)">
    <rect width="1300" height="180" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">🎯 戰術框架核心思想</text>
    <text x="650" y="65" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">涵蓋從身份建立 (E-E-A-T)、實體連結 (Entity)、內容結構化 (Structured Data) 到即時索引 (Indexing) 的完整鏈條</text>
    <text x="650" y="90" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">讓人和 AI 都能秒懂、秒信、秒用你的內容</text>
    
    <!-- 五大目標 -->
    <g transform="translate(30, 110)">
      <text x="20" y="20" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#667eea">1. 建立權威身份 (11, 12)：</text>
      <text x="250" y="20" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">讓AI知道你是誰，且值得信賴</text>
      
      <text x="20" y="45" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#667eea">2. 結構化內容 (13, 14, 17, 18)：</text>
      <text x="250" y="45" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將內容拆解成AI最喜歡的問答、主張、數據等原子化模塊</text>
      
      <text x="650" y="20" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#667eea">3. 搶佔高潛力渠道 (15, 16)：</text>
      <text x="880" y="20" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在AI高度依賴的資訊來源中預埋內容</text>
      
      <text x="650" y="45" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#667eea">4. 加速資訊傳遞 (19)：</text>
      <text x="880" y="45" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">確保最新內容能最快被AI生態系統捕獲</text>
      
      <text x="350" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#667eea">5. 衡量最終成效 (20)：</text>
      <text x="580" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">讓AI帶來的價值變得可見、可衡量</text>
    </g>
  </g>

  <!-- 11. Author Graph 對齊 -->
  <g transform="translate(50, 440)">
    <rect width="1300" height="950" rx="20" fill="url(#authorGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="920" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣1️⃣ Author Graph 對齊（作者名片＋Author Schema）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">在AI眼中，內容的可信度極大程度取決於作者的權威性</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">向AI（及搜尋引擎）清晰地聲明：「這篇文章的作者是『某個具備專業知識的真實人類』」</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將這位作者與其在網路上的其他專業足跡（社群帳號、其他出版物、學術背景等）連結起來</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">建立穩固的 E-E-A-T (專業、經驗、權威、信任) 信號</text>
    </g>

    <!-- 詳解與補充建議 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="580" height="280" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 作者名片 (Author Bio Box)：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在每篇文章的結尾（或開頭）顯示</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">包含作者照片、姓名、職稱和簡介的區塊</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這不僅是給讀者看的，更是給AI看的結構化信號</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. Author Schema (Person type)：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">透過 JSON-LD 結構化資料</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在程式碼層面告訴機器這位作者是誰</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這比單純的HTML文本更精準</text>

      <rect x="600" y="0" width="580" height="280" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 建立專屬作者頁：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">每個作者都應該有一個獨立的個人介紹頁面</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">(/author/author-name/)</text>
      
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. sameAs 的極致運用：</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">連結到所有能證明其專業身份的權威網址</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">LinkedIn、X (Twitter)、學術論文頁面</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">行業協會會員頁面、維基百科條目等</text>
      
      <text x="630" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">3. 全站一致性：</text>
      <text x="630" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">確保作者姓名在全站的拼寫與用法完全一致</text>
    </g>

    <!-- 具體操作流程 -->
    <g transform="translate(40, 540)">
      <rect x="0" y="0" width="1220" height="180" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">1. 設計作者名片區塊：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在文章模板 (e.g., single.php) 中加入作者名片區塊，動態抓取作者資訊</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">2. 建立作者專頁：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">為每位作者建立詳細的個人頁面</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">3. 部署 Author Schema：</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在文章頁面的 &lt;head&gt; 中或透過 Google Tag Manager 注入 JSON-LD</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">4. 驗證：</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用 Google 的複合式搜尋結果測試工具檢查 Schema 是否正確部署</text>
    </g>

    <!-- JSON-LD 範例 -->
    <g transform="translate(40, 740)">
      <rect x="0" y="0" width="1220" height="160" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 Author Schema JSON-LD 範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">{</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@context": "https://schema.org",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@type": "Article",</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "author": {</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "@type": "Person",</text>
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "name": "作者姓名",</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "url": "https://yourdomain.com/author/author-name/",</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "jobTitle": "作者職稱",</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "sameAs": [</text>
      <text x="650" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">      "https://www.linkedin.com/in/authorprofile/",</text>
      <text x="650" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">      "https://twitter.com/authorhandle"</text>
      <text x="650" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    ]</text>
    </g>
  </g> 
 <!-- 12. Entity sameAs -->
  <g transform="translate(50, 1420)">
    <rect width="1300" height="850" rx="20" fill="url(#entityGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="820" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣2️⃣ Entity sameAs（品牌/作者/產品繫結）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">消除歧義，幫助AI正確地將你的內容歸入其知識圖譜中的正確節點</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">網路充滿了同名但不同實體的人、品牌或產品</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">sameAs 是對AI最直接的宣告：「我這裡提到的『蘋果』，指的是『蘋果公司 (Apple Inc.)』，而不是水果。」</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">提升內容的相關性與權威性</text>
    </g>

    <!-- 詳解與補充建議 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在 Organization、Person、Product 等</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Schema 類型中，使用 sameAs 屬性</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將你的實體連結到一個公認的、</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">權威的、唯一的識別符 URL</text>

      <rect x="600" y="0" width="580" height="220" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 優先連結至知識圖譜來源：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Wikidata 是最重要的 sameAs 連結目標</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">其次是 Wikipedia、產業權威資料庫</text>
      
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. 建立自己的 Wikidata 項目：</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">如果您的品牌、創辦人或產品尚未在</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Wikidata 中有條目，請主動去建立一個</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">這是最根本的實體宣告</text>
    </g>

    <!-- 具體操作流程 -->
    <g transform="translate(40, 480)">
      <rect x="0" y="0" width="1220" height="160" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">1. 尋找權威 URL：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 在 Wikidata (https://www.wikidata.org) 搜尋您的品牌/作者/產品，找到對應的 Q-ID 頁面 URL</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 尋找 Wikipedia 頁面、官方 LinkedIn 公司頁面、X 帳號等</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">2. 注入 Schema：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在網站首頁或關於我們頁面部署 Organization Schema</text>
    </g>

    <!-- Organization JSON-LD 範例 -->
    <g transform="translate(40, 660)">
      <rect x="0" y="0" width="1220" height="140" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 Organization Schema JSON-LD 範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">{</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@context": "https://schema.org",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@type": "Organization",</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "name": "柏瀚國際科技有限公司",</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "sameAs": [</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://www.wikidata.org/wiki/Qxxxx",</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://www.linkedin.com/company/yourcompany/",</text>
      <text x="650" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://www.facebook.com/yourcompany/"</text>
      <text x="650" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  ]</text>
    </g>
  </g>

  <!-- 13. Q-List 對映 -->
  <g transform="translate(50, 2300)">
    <rect width="1300" height="850" rx="20" fill="url(#qlistGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="820" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣3️⃣ Q-List 對映（問題庫→貼文/長文標籤）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">AI 的核心是問答，主動標記內容為特定問題的「答案」</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將社群上的零散貼文或長文中的段落，主動標記為特定問題的「答案」</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">當AI模型（如 Perplexity 或 Gemini）尋找某個問題的答案時</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">可以直接定位到你已經標記好的內容片段，大幅提高被引用機率</text>
    </g>

    <!-- 詳解 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="1220" height="180" rx="12" fill="rgba(254, 225, 64, 0.15)" stroke="#fee140" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">1. 建立問題庫 (Q-List)：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">收集與您專業領域相關的核心問題、長尾問題、使用者常見疑問</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">2. 內容對映：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在發布社群貼文或部落格長文時，思考「這段內容回答了問題庫裡的哪個問題？」</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">3. 標籤化：</text>
      <text x="130" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用社群平台的 Hashtag 或文章內的標題/小標題，明確地將問題與內容對應</text>
    </g>

    <!-- 補充建議 -->
    <g transform="translate(40, 440)">
      <rect x="0" y="0" width="580" height="200" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 來源多樣化：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Google PAA (People Also Ask)</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">AnswerThePublic、競爭對手的 FAQ</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">客戶服務日誌、社群留言等</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 動態更新：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">隨著市場趨勢和使用者行為的變化</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">而持續更新問題庫</text>

      <!-- 具體操作流程 -->
      <rect x="600" y="0" width="580" height="200" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 建立問題庫：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用 Google Sheets 或 Airtable</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">建立包含「問題」、「關鍵字」、</text>
      <text x="630" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">「對應內容URL」、「狀態」的資料庫</text>
      
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 內容創作流程整合：</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在內容發想階段，就從問題庫中</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">選取要回答的問題</text>
    </g>

    <!-- 社群貼文與長文操作 -->
    <g transform="translate(40, 660)">
      <rect x="0" y="0" width="1220" height="140" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📱 社群貼文與長文操作</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">社群貼文操作：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 貼文開頭： Q：如何提升本地SEO排名？ A：...</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 使用 Hashtag: #本地SEO問答 #Google商家檔案優化</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">長文操作：</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 文章結構： 使用 H2/H3 標題直接放入問題本身</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 文末總結： 在文末放一個「本文回答了以下問題」的列表</text>
      <text x="650" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">  並使用錨點連結跳轉到相應段落</text>
    </g>
  </g>  <!-- 
14. FAQ 連發 -->
  <g transform="translate(50, 3180)">
    <rect width="1300" height="950" rx="20" fill="url(#faqGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="920" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣4️⃣ FAQ 連發（每主題 8–12 題，30字內直球）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">為AI提供最容易「消化」和「引用」的素材</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">短、平、快的直球問答格式，極易被AI模型直接抓取作為其生成答案的來源</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這種格式滿足了AI對準確、簡潔資訊的偏好</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">提供最核心、最直接的回答，不囉嗦</text>
    </g>

    <!-- 詳解 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="1220" height="180" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa71cd" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa71cd">1. 針對一個核心主題：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（例如：「網站速度優化」），設計 8-12 個高度相關的子問題</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa71cd">2. 每個問題的答案都控制在30字以內：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（約60個英文字元）提供最核心、最直接的回答</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa71cd">3. 可以在一篇長文中以 FAQ 區塊呈現：</text>
      <text x="350" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">也可以在社群上連續發布</text>
    </g>

    <!-- 補充建議 -->
    <g transform="translate(40, 440)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 部署 FAQPage Schema：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">如果在網頁上使用此策略</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">務必用 FAQPage Schema</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將這些問答包裝起來</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">這是給AI的「官方菜單」</text>
      
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 避免行銷語言：</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">答案應極度客觀、中立</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">像是在編寫教科書或詞典</text>

      <!-- 具體操作流程 -->
      <rect x="600" y="0" width="580" height="220" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 選定主題：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">選擇一個您業務的核心主題</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 設計問題：</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">圍繞該主題設計 8-12 個</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">由淺入深、環環相扣的問題</text>
      
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 撰寫精簡答案：</text>
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">為每個問題撰寫不超過30字的精準答案</text>
    </g>

    <!-- FAQPage Schema 範例 -->
    <g transform="translate(40, 680)">
      <rect x="0" y="0" width="1220" height="220" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 FAQPage Schema JSON-LD 範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">{</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@context": "https://schema.org",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@type": "FAQPage",</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "mainEntity": [{</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "@type": "Question",</text>
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "name": "問題一的完整文本？",</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "acceptedAnswer": {</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">      "@type": "Answer",</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">      "text": "這是問題一的超精簡答案，控制在30字內。"</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    }</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  }, {</text>
      <text x="650" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "@type": "Question",</text>
      <text x="650" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "name": "問題二的完整文本？",</text>
      <text x="650" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "acceptedAnswer": {</text>
      <text x="650" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">      "@type": "Answer",</text>
      <text x="650" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">      "text": "這是問題二的超精簡答案，同樣非常簡短。"</text>
      <text x="650" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    }</text>
    </g>
  </g>

  <!-- 15. GBP Q&A 同步 -->
  <g transform="translate(50, 4160)">
    <rect width="1300" height="750" rx="20" fill="url(#evidenceGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="720" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣5️⃣ GBP Q&amp;A 同步（2–3 條短問短答）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">在AI的本地資料庫中「餵」資料</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Google Business Profile (GBP) 是 Google 知識圖譜中關於本地實體最權威的資訊來源之一</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">AI在回答具有「本地意圖」的查詢時，會高度優先參考 GBP 的內容</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">在此預埋問答，等於是直接向AI的本地資料庫中「餵」資料</text>
    </g>

    <!-- 詳解與補充建議 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(255, 193, 7, 0.15)" stroke="#ffc107" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffc107">1. 主動在自己的 GBP 檔案的</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">「問與答」區塊，使用不同帳號</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（可以請朋友或用自己的其他帳號）</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">提出 2-3 個使用者最常問的核心問題</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffc107">2. 然後用官方業主身份</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">提供最精準、簡潔的官方回答</text>

      <rect x="600" y="0" width="580" height="240" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 鎖定高價值問題：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">選擇那些能直接導向轉化的問題</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">例如：「你們有提供ＸＸＸ服務嗎？」</text>
      <text x="630" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">「停車方便嗎？」、「週末營業時間是幾點？」</text>
      
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 關鍵字植入：</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在問題和答案中自然地置入</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">核心服務關鍵字和地名</text>
      
      <text x="630" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 定期維護：</text>
      <text x="630" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">定期檢查是否有真實使用者提問，並及時回答</text>
    </g>

    <!-- 具體操作流程 -->
    <g transform="translate(40, 500)">
      <rect x="0" y="0" width="1220" height="200" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 登入管理GBP的Google帳號</text>
      
      <text x="30" y="80" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 進入 GBP 管理後台，找到「問與答」功能</text>
      
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 自己提問（或請他人提問）：</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">提出準備好的 2-3 個核心問題</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">4. 切換業主身份回答：</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">提供官方、精簡、有幫助的答案</text>
      
      <text x="650" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">5. 按讚答案：</text>
      <text x="650" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">為這個官方答案點讚，使其更容易被看到</text>
    </g>
  </g>  <!-- 16.
 Evidence Thread 四連貼 -->
  <g transform="translate(50, 4940)">
    <rect width="1300" height="850" rx="20" fill="url(#authorGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="820" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣6️⃣ Evidence Thread 四連貼（主張→數據→圖表→來源）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">AI極度重視有證據支持的論點</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">AI（特別是像 Perplexity 這種強調來源的 AI）極度重視有證據支持的論點</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這種「推文串」格式，將一個論點的完整邏輯鏈（主張、數據、視覺化證據、信源）清晰地呈現出來</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">構成一個完美的、可被引用的資訊包</text>
    </g>

    <!-- 詳解 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="1220" height="200" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 在 X (Twitter) 或 Threads 等平台上：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">以連續貼文的形式發布一個主題</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 第一貼（主張）：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">提出一個明確、有力的核心論點</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 第二貼（數據）：</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">提供支持該論點的關鍵數據或事實</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">4. 第三貼（圖表）：</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將數據視覺化，製作成簡單易懂的圖表或圖片</text>
      
      <text x="650" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">5. 第四貼（來源）：</text>
      <text x="650" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">附上數據的原始來源連結（權威報告、學術論文、官方統計等）</text>
      <text x="650" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">或指向自己網站上更詳細的分析文章</text>
    </g>

    <!-- 補充建議與操作流程 -->
    <g transform="translate(40, 460)">
      <rect x="0" y="0" width="580" height="200" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 強強聯合：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">如果引用的是知名機構的報告</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在貼文中 @ 該機構的官方帳號</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">增加曝光和可信度</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. 內部連結閉環：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">第四貼的來源連結，最好是指向</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">你自己網站上一篇對此數據有深入解讀的文章</text>

      <rect x="600" y="0" width="580" height="200" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">1. 確定主張：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">選擇一個你想證明的、有價值的行業觀點</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">2. 尋找證據：</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">查找權威的第三方數據來支持你的主張</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">3. 製作圖卡：</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用 Canva 或其他工具，將核心數據製作成清晰的圖表</text>
    </g>

    <!-- 四連貼範例 -->
    <g transform="translate(40, 680)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📱 四連貼範例結構</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">🧵 1/4 主張：「AI驅動的搜尋將在2025年成為主流」</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">📊 2/4 數據：根據最新研究，AI搜尋使用率年增長200%</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">📈 3/4 圖表：[附上視覺化增長趨勢圖]</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">🔗 4/4 來源：完整報告連結 + 我們的深度分析</text>
    </g>
  </g> 
 <!-- 17. 可視化證據 -->
  <g transform="translate(50, 5820)">
    <rect width="1300" height="750" rx="20" fill="url(#entityGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="720" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣7️⃣ 可視化證據（圖卡＋一句可引說明＋自然語言 ALT）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">現代AI是多模態的，能夠理解圖片內容</text>

    <!-- 目標與詳解 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="280" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">為了讓圖片本身及其附帶的文本</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">都能被AI準確解讀和引用</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">一張好的圖卡，對AI來說，勝過千言萬語</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">📋 詳解：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">1. 圖卡 (Infographic/Chart)：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">   將複雜數據或流程，製作成視覺化的圖片</text>
      
      <text x="30" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">2. 一句可引說明 (Citable Caption)：</text>
      <text x="30" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">   用一句話精準概括這張圖的核心結論</text>
      
      <text x="30" y="240" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">3. 自然語言 ALT：</text>
      <text x="30" y="260" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">   用完整的、描述性的自然語言來解釋圖片內容</text>

      <rect x="600" y="0" width="580" height="280" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 品牌化與一致性：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">所有圖卡都應有統一的品牌識別</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（Logo、顏色、字體）</text>
      
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 數據來源標註：</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在圖卡上不明顯處標註數據來源</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">增加可信度</text>
      
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 文件名優化：</text>
      <text x="630" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">圖片文件名也應具備描述性</text>
      <text x="630" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">例如 ai-search-traffic-share-2025-chart.png</text>
    </g>

    <!-- 具體操作流程與ALT範例 -->
    <g transform="translate(40, 400)">
      <rect x="0" y="0" width="580" height="200" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 內容轉化：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將文章中的核心數據、步驟、觀點</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">轉化為圖卡設計稿</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. 設計製作：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用工具製作圖卡</text>
      
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">3. 發布：</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">確保 ALT 文字已正確填寫</text>

      <rect x="600" y="0" width="580" height="200" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📝 ALT 文字範例</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">說明文字：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">研究顯示，到2025年，AI驅動的搜尋</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將成為最主要的流量來源。</text>
      
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">ALT 文字：</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">一張顯示不同流量來源佔比的圓餅圖，</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">其中AI搜尋佔40%，自然搜尋佔35%，</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">社群推薦佔15%，其他佔10%。</text>
    </g>
  </g>

  <!-- 18. Canonical 深連結 -->
  <g transform="translate(50, 6600)">
    <rect width="1300" height="750" rx="20" fill="url(#qlistGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="720" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣8️⃣ Canonical 深連結（頁內 #claim-xx 錨點）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">主動為AI創造精準的「引用錨點」</text>

    <!-- 目標與AI關聯性 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Google SGE 和其他AI問答引擎在引用來源時，越來越傾向於直接跳轉到頁面中的特定段落（Text Fragment）</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">此策略是主動為AI創造精準的「引用錨點」</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">讓AI可以百分之百準確地引用你文章中的某一個具體主張或數據</text>
    </g>

    <!-- 詳解與補充建議 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(254, 225, 64, 0.15)" stroke="#fee140" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 詳解</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">1. 識別核心主張：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在一篇長文中，識別出幾個最核心、</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">最可能被引用的主張 (claim) 或數據點</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">2. 添加唯一 ID：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">為包裹這些主張的 HTML 元素</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（如 &lt;div&gt; 或 &lt;p&gt;）添加一個唯一的 ID</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">例如 id="claim-01"</text>
      
      <text x="30" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">3. 使用深連結：</text>
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">https://yourdomain.com/article/#claim-01</text>

      <rect x="600" y="0" width="580" height="240" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 結合 ClaimReview Schema：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">如果你的內容是事實查核或對某個主張</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">進行評估，可以結合 ClaimReview Schema</text>
      
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 目錄生成：</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在文章開頭可以根據這些錨點</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">自動生成一個目錄</text>
      
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 語義化 ID：</text>
      <text x="630" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">ID 名稱最好有意義，例如</text>
      <text x="630" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">id="ai-traffic-2025-statistic"</text>
    </g>

    <!-- 具體操作流程與HTML範例 -->
    <g transform="translate(40, 500)">
      <rect x="0" y="0" width="1220" height="200" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程與HTML範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">1. 識別關鍵句：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在文章中找到你希望被精準引用的句子或段落</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">2. 添加 ID：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">編輯 HTML，為該段落的標籤添加 ID</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">&lt;p id="ai-traffic-growth-rate"&gt;根據我們的研究，</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">AI驅動的搜尋流量年增長率預計將達到200%。&lt;/p&gt;</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">3. 創建深連結：</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在你需要引用它的地方，使用連結</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">https://.../page/#ai-traffic-growth-rate</text>
      
      <text x="650" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">4. 確保 Canonical 正確：</text>
      <text x="650" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">確保頁面的 &lt;link rel="canonical"&gt;</text>
      <text x="650" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">指向的是不含 # 的主 URL</text>
    </g>
  </g>
  
  <!-- 19. IndexNow/WebSub -->
  <g transform="translate(50, 7380)">
    <rect width="1300" height="650" rx="20" fill="url(#faqGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣9️⃣ IndexNow／WebSub（縮短可見延遲）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">將可見延遲從幾天縮短到幾分鐘</text>

    <!-- 目標與詳解 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">AI 模型的知識更新依賴其底層的搜尋索引</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">內容發布後，越快被索引，就越快能成為</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">AI的潛在知識來源</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">📋 詳解：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">1. IndexNow: 由微軟 Bing 和 Yandex 發起</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">   Google 也已宣布支援</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">2. WebSub: 一個更早的開放協議</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">   同樣允許內容即時推送</text>

      <rect x="600" y="0" width="580" height="220" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 優先用於時效性強的內容：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">新聞、活動公告、產品上架、緊急聲明等</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 整合到 CMS：</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用外掛（如 AIOSEO, Rank Math 等</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">WordPress 外掛已支援）</text>
      
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 不要濫用：</text>
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">只推送真正有變更的 URL</text>
    </g>

    <!-- 具體操作流程 -->
    <g transform="translate(40, 340)">
      <rect x="0" y="0" width="1220" height="240" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程（以 IndexNow 為例）</text>
      
      <text x="30" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 生成 API 金鑰：</text>
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在你的網站根目錄下放置一個 txt 檔案，檔名為你的 API 金鑰（自己生成的一組字串）</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">內容也是該字串。例如，檔名 a1b2c3d4e5f6.txt，內容 a1b2c3d4e5f6</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 發送請求：</text>
      <text x="30" y="145" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">當有新文章 https://yourdomain.com/new-post/ 發布時，向以下 URL 發送 GET 請求：</text>
      
      <text x="30" y="175" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">https://api.indexnow.org/indexnow?url=https://yourdomain.com/new-post/&amp;key=a1b2c3d4e5f6</text>
      <text x="30" y="195" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（不同搜尋引擎有自己的端點，但 api.indexnow.org 會分發）</text>
      
      <text x="30" y="225" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 自動化：</text>
      <text x="130" y="225" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">使用 CMS 外掛或設定伺服器端的腳本，在發布/更新文章時自動觸發此 API 請求</text>
    </g>
  </g>

  <!-- 20. UTM for AI 來源分流 -->
  <g transform="translate(50, 8060)">
    <rect width="1300" height="650" rx="20" fill="url(#evidenceGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">2️⃣0️⃣ UTM for AI 來源分流（chatgpt/perplexity/gemini/claude）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">精準追蹤AI帶來的效益</text>

    <!-- 目標與詳解 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 目標與AI關聯性</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這是一個數據追蹤與分析的戰術</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">當AI在其回答中引用你的網站連結時</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">它通常不會自帶推薦來源資訊</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">📋 詳解：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這導致在 Google Analytics (GA) 中</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">這部分流量可能被歸為「Direct」（直接流量）</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">讓你無法衡量AI帶來的效益</text>
      
      <text x="30" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">此策略是主動教育使用者，在與AI互動時</text>
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">使用帶有UTM參數的連結</text>

      <rect x="600" y="0" width="580" height="240" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 這是一個長期教育過程：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">很難強制所有使用者都這樣做</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">但可以在你的社群、教學文章中大力倡導</text>
      
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 建立一個 UTM 生成器：</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在你網站上提供一個簡單的工具</text>
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">讓使用者可以方便地為你的文章連結</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">生成用於分享到AI的UTM連結</text>
      
      <text x="630" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 反向應用：</text>
      <text x="630" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">分析你的受眾對哪個AI工具更感興趣</text>
    </g>

    <!-- 具體操作流程 -->
    <g transform="translate(40, 360)">
      <rect x="0" y="0" width="1220" height="220" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 具體操作流程</text>
      
      <text x="30" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 定義你的UTM結構：</text>
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• utm_source: 用於標識是哪個AI平台，如 perplexity, chatgpt, gemini</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• utm_medium: 可以統一設為 ai_referral 或 generative_ai</text>
      <text x="30" y="115" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• utm_campaign: 用於標識內容主題或活動，如 q3_seo_report</text>
      
      <text x="30" y="145" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 創建範例連結：</text>
      <text x="30" y="165" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">https://yourdomain.com/article/?utm_source=perplexity&amp;utm_medium=ai_referral&amp;utm_campaign=q3_seo_report</text>
      
      <text x="30" y="195" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 推廣與教育：</text>
      <text x="130" y="195" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在文章結尾寫道：「想看看 Perplexity AI 如何總結本文嗎？複製此連結並貼給它：[帶UTM的連結]」</text>
    </g>
  </g>

  <!-- 總結 -->
  <g transform="translate(50, 8740)">
    <rect width="1300" height="200" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">🎯 總結：完美的閉環戰術體系</text>
    
    <text x="50" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">這份清單的後十項戰術，完美地構成了一個從內容創作、技術部署到成效分析的閉環。它們共同的目標是：</text>
    
    <text x="50" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">1. 建立權威身份 (11, 12)：</text>
    <text x="300" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#666">讓AI知道你是誰，且值得信賴</text>
    
    <text x="50" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">2. 結構化內容 (13, 14, 17, 18)：</text>
    <text x="300" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#666">將內容拆解成AI最喜歡的問答、主張、數據等原子化模塊</text>
    
    <text x="50" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">3. 搶佔高潛力渠道 (15, 16)：</text>
    <text x="300" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#666">在AI高度依賴的資訊來源（如GBP、社群）中預埋內容</text>
    
    <text x="50" y="175" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">4. 加速資訊傳遞 (19)：</text>
    <text x="300" y="175" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#666">確保你的最新內容能最快被AI生態系統捕獲</text>
    
    <text x="650" y="175" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">5. 衡量最終成效 (20)：</text>
    <text x="850" y="175" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#666">讓AI帶來的價值變得可見、可衡量</text>
  </g>

  <!-- 頁尾公司資訊 -->
  <g transform="translate(0, 8970)">
    <rect width="1400" height="160" fill="url(#headerGradient)" filter="url(#shadow)"/>
    <rect x="20" y="20" width="1360" height="120" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 公司標誌 -->
    <circle cx="120" cy="80" r="40" fill="#667eea"/>
    <text x="120" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="20" font-weight="bold" fill="white">SEO</text>
    <text x="120" y="90" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="white">優化王</text>
    
    <!-- 公司資訊 -->
    <text x="180" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" font-weight="bold" fill="#2d3748">柏瀚國際科技有限公司 SEOKING INTERNATIONAL TECHNOLOGY CO.</text>
    <text x="180" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">創辦人：Roger Lin（林成基） | 台灣頂尖 AI 搜尋優化 AISO 360™</text>
    <text x="180" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">統一編號：27305928 | 地址：104 台北市中山區新生北路二段31之1號9樓之7</text>
    <text x="180" y="115" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">📞 02-2563-4727 | 📧 <EMAIL> | 🌐 https://seoking.com.tw</text>
    
    <!-- 版權資訊 -->
    <text x="1200" y="80" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#667eea">© 2025 AISO 360™</text>
    <text x="1200" y="100" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#667eea">All Rights Reserved</text>
  </g>

  <!-- 浮水印 -->
  <text x="1350" y="9150" text-anchor="end" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
        font-size="12" fill="rgba(102, 126, 234, 0.3)">Generated by AISO 360™ Framework</text>
</svg>