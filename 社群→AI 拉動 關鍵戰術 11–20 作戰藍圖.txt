﻿社群→AI 拉動 關鍵戰術 11–20 做成「策略目標 → 交付物規格 → 操作SOP → QA檢核 → KPI/儀表板」五段式，直接可落地。內容以你既有 AISO 360™ 工具鏈（n8n/GTM/GA4/Repo/Notion）為預設環境。
________________


11) Author Graph 對齊（作者名片＋Author Schema）
策略目標：把「人（作者）—內容—組織」三者關係機器可讀化，強化 EEAT 與可歸因能力。
交付物規格
* author_profile.md：姓名｜職銜｜一行定位｜3-5條資歷｜代表作｜聯繫方式｜授權宣告。
* person.jsonld（每位作者 1 檔）：@type: Person，worksFor（Organization）、jobTitle、knowsAbout、sameAs[]、url、image、alumniOf、award、hasCredential、authorOf。
* 前端元件：作者名片（byline/頁尾/作者頁），對應 JSON-LD 自動注入。
操作SOP
1. 盤點作者 → 建立 author_registry.csv（author_id、display_name、lang、job_title、topics、sameAs、headshot_url）。
2. 以 Git 版本化作者檔（/data/authors/{author_id}.json）。
3. 文章模版統一 byline：author_id → 客製名片 + Person JSON-LD。
4. 與 Organization JSON-LD 串聯（worksFor / memberOf）。
QA檢核
* Rich Results/Schema 驗證無錯；byline 顯示一致；同一作者名稱與拼寫全站唯一。
* 文章頁的 datePublished/modified 與作者更新紀錄一致。
KPI/儀表板
* 作者署名覆蓋率 ≥ 95%
* 含作者 JSON-LD 的頁面比重 ≥ 90%
* 作者名 + 品牌名的聯合提及在 AI 回答中的準確率（LAR）↑
________________


12) Entity sameAs（品牌/作者/產品繫結）
策略目標：統一實體身分，降低同名歧義，提升模型對「你=這個品牌/人/產品」的信心。
交付物規格
* sameas_registry.csv：entity_id、entity_type（Organization/Person/Product/Service）、canonical_url、sameAs[]（權重排序：官網→政府/工商→Wiki/DB→主流社媒→影音/程式碼庫）。
* organization.jsonld：@type: Organization，含 sameAs[]、founder、foundingDate、legalName、contactPoint。
操作SOP
1. 列出每個實體的「權威頁」與「社群頁」；死鏈/跳轉一律清掉。
2. 只保留可長期穩定 200 的 URL；避免短鏈當 sameAs。
3. 在官網全站範本注入 Organization/Person/Product 的 sameAs。
QA檢核
* sameAs 不混用追蹤參數；不指向重定向頁。
* entity_id 與 schema @id 對齊。
KPI/儀表板
* 實體覆蓋率（有 sameAs 的實體/總實體）≥ 95%
* AI 回答中品牌/作者對應外部資料頁的一致率↑
________________


13) Q-List 對映（問題庫 → 貼文/長文標籤）
策略目標：把「問題池」結構化映射到內容矩陣，用於 GEO 覆蓋與社群分發。
交付物規格
* q_list.csv：q_id, question, intent(4I), cluster, subcluster, priority, target_url, tag[], answer_status(draft/live), owner, last_review.
* 內容標籤規格：#Q:q_id、#Cluster:xxx、#Intent:Informational/Investigative/...。
操作SOP
1. 生成/整併 50–200 條核心問句 → 依 4I+語音意圖分群。
2. 每題對應一個目標落地頁（或新建），同步在社群排程。
3. 每週至少 10 題轉為「短貼文 + 長文錨點」的雙格式上線。
QA檢核
* q_id 不重複；每題都有 owner；每 90 天複審 last_review。
KPI/儀表板
* GEO 覆蓋率（命中/總題）
* 每題到頁面的 CTR、停留、助理引用次數（ACR）
________________


14) FAQ 連發（每主題 8–12 題，30字內直球）
策略目標：用「極短、可朗讀、可抽取」的 FAQ 提升 AIO/VSO 命中。
交付物規格
* faq_batch.md/csv：topic, q, a_short(≤30字), a_long(≤120字), source_url, q_id。
* FAQPage JSON-LD（多題）。
操作SOP
1. 從 Q-List 抽 8–12 題高意圖問句 → 撰寫 a_short（≤30字直球）。
2. 同頁加入 a_long 版本 + 來源連結（提升可引性）。
3. 以模組化區塊插入到 Pillar/Cluster 頁；同步社群輪播。
QA檢核
* a_short 禁止贅詞與品牌硬廣；每題唯一答案。
* 語句 20–30 秒可朗讀，多語系一致。
KPI/儀表板
* 精選摘要/FAQ Rich Result 命中數
* 語音問句命中率（VSO_HIT）
________________


15) GBP Q&A 同步（2–3 條短問短答）
策略目標：把主題 Q&A 下沉到本地場景，讓語音/地圖查詢可直接命中。
交付物規格
* 每週固定 2–3 條 Q&A：question(≤18字) / answer(≤40字) / local_modifier（地區/服務時段）。
* 與官網 FAQ 同題同義不同字，避免重複內容疲勞。
操作SOP
1. 從本週 FAQ 批次選題 → 加上地區/時段修飾詞。
2. 上架 GBP Q&A，同步官網 FAQPage「可朗讀」標記。
3. 圖片（門面/團隊/設備）每週至少 1 張更新。
QA檢核
* NAP 一致；分類選擇正確；禁止過度銷售詞。
KPI/儀表板
* GBP Q&A 展示/互動數
* 來電/導覽/網站點擊 的增幅對比 Q&A 上線週
________________


16) Evidence Thread 四連貼（主張→數據→圖表→來源）
策略目標：把「可引用證據」打包成社群串，提升 CS_SHARE 與可引性（LLM Quotability）。
交付物規格
* claims.csv：claim_id, claim_text(≤40字), metric, value, method, timeframe, source_url, chart_asset, alt_text.
* 貼文結構：#1 主張、#2 數據、#3 圖表、#4 來源（含短鏈）。
操作SOP
1. 由白皮書/案例抽 3–5 條可驗證主張 → 配對量化數據。
2. 產圖（可復用圖卡模板）；撰寫一句可引說明與 ALT。
3. 以 4 連貼形式在 X/FB/LI 發佈；claim_id 對應頁內錨點。
QA檢核
* 來源至少 2 個（原始/二手）；數字可追溯；圖表標註量測方法。
KPI/儀表板
* 二次轉貼率、被引用段落數（CS_SHARE）
* 對應頁面停留與助理引用提升（ACR/LAR）
________________


17) 可視化證據（圖卡＋一句可引說明＋自然語言 ALT）
策略目標：讓模型與讀者「看得到、讀得懂、拿得到」證據。
交付物規格
* 圖卡命名：{topic}_{claim_id}_{yyyymm}.png。
* 圖卡文案：標題 ≤ 12 字；副標 ≤ 24 字；資料來源置底。
* ALT 模版：[圖表類型] 顯示 [指標] 在 [期間/樣本] 的 [趨勢/差異]；關鍵數值：[x]；資料來源：[來源]。
* ImageObject JSON-LD：creator/datePublished/caption/license/contentUrl/thumbnail。
操作SOP
1. 設計統一圖卡版型；Figma/Canva 模版 + 批次匯出。
2. 發佈頁插入圖卡 + ImageObject JSON-LD；社群同步簡化版。
3. 以 claim_id 映射到長文錨點。
QA檢核
* ALT 句子通順、無專有名詞堆疊；圖片壓縮不失真（LCP 友善）。
KPI/儀表板
* 圖卡被引用次數、圖片搜尋曝光
* 附帶 ALT 的頁面 ACR 貢獻度
________________


18) Canonical 深連結（頁內 #claim-xx 錨點）
策略目標：讓外部/AI 直接指向「可引用段落」，提升抽取效率與可監測性。
交付物規格
* 錨點規則：id="claim-001"（每頁遞增），對應 data-claim-id 與 claim_id。
* 目錄（TOC）自動生成，顯示錨點。
* Canonical 仍指向頁面 URL，對外分享用深連結（#claim-xxx）。
操作SOP
1. Markdown/React 組件為 H2/H3 自動注入穩定錨點；
2. 於 Evidence Thread，以深連結回指；
3. GA4 自訂事件 deep_link_open（當 URL 含 #claim-）。
QA檢核
* 錨點唯一；改版不變動 claim_id；404/重導不影響錨點。
KPI/儀表板
* 深連結點擊率、深連結入口的平均停留/跳出
* 以深連結被 AI/社群引用的次數
________________


19) IndexNow／WebSub（縮短可見延遲）
策略目標：把「新/改」內容即時推送給搜尋/聚合端，降低 Time-to-Crawl/Index。
交付物規格
* indexnow_key.txt 與自動推送腳本（n8n/GitHub Actions）。
* RSS/Atom Feed + WebSub Hub 通知推送。
操作SOP
1. 取得 IndexNow 金鑰 → 部署於根目錄；
2. 內容發佈/更新後：批次 POST 至 IndexNow（單條或清單）；
3. Feed 變更即觸發 WebSub Hub 通知；
4. 失敗重試（指數回退），每日匯總報表。
QA檢核
* 金鑰可讀；200/202 回應率 ≥ 99%；URL 無 301 連續跳轉。
KPI/儀表板
* 首次收錄時間（TTCI）中位數
* 更新 → 快照刷新時間縮短率
IndexNow Request（示例）
{
  "host": "www.seoking.com.tw",
  "key": "YOUR_INDEXNOW_KEY",
  "keyLocation": "https://www.seoking.com.tw/YOUR_INDEXNOW_KEY.txt",
  "urlList": [
    "https://www.seoking.com.tw/aisoseo/faq#claim-012",
    "https://www.seoking.com.tw/cases/aiso-360"
  ]
}


________________


20) UTM for AI 來源分流（chatgpt/perplexity/gemini/claude）
策略目標：把「AI 助理帶來的流量/轉換」拆分到引擎/模型層級進行投資回收評估。
交付物規格
* UTM 命名準則
   * utm_source=chatgpt|perplexity|gemini|claude
   * utm_medium=assistant（或 ai-referral）
   * utm_campaign={topic|pillar}
   * utm_content={claim_id|q_id}
   * 自訂參數：ai_engine、ai_model、ai_surface(answer|source|card)、ai_session（短鏈ID）
* 短鏈池：為每個 AI 引擎建立專屬短鏈前綴（例：s.seoking.tw/cgpt/...、.../ppx/...）。
* GA4 維度：自訂維度 ai_engine/ai_model/ai_surface；自訂事件 ai_click。
操作SOP
1. 在社群/FAQ/聲明頁對外連結一律帶上述 UTM；
2. 以短鏈服務（或自架）對應 ai_session，避免 referrer 遺失；
3. GTM 以查詢參數解析 → 設定 GA4 自訂維度；
4. BigQuery 建立歸因視圖：ai_engine → 目標/交易/詢問。
QA檢核
* 所有可控外連均帶 UTM；短鏈 301 單跳；
* GA4 維度命中率 ≥ 95%，與後端成交/線索相互校驗。
KPI/儀表板
* AI 來源流量/新客占比/轉換率
* ACR 與 LAR 對照 ai_engine 的貢獻度
* 單引擎 CPL/CPO/ROAS
________________


附：範本片段（可直接貼用）
A. Person JSON-LD
<script type="application/ld+json">
{
  "@context":"https://schema.org",
  "@type":"Person",
  "@id":"https://www.seoking.com.tw/#person-roger",
  "name":"Roger Lin（林成基）",
  "jobTitle":"創辦人｜AI 搜尋優化顧問",
  "worksFor":{"@id":"https://www.seoking.com.tw/#org"},
  "url":"https://www.seoking.com.tw/about/roger-lin",
  "image":"https://www.seoking.com.tw/assets/roger.jpg",
  "knowsAbout":["AI SEO","AIO/GEO/VSO/AVO","EEAT"],
  "sameAs":[
    "https://www.linkedin.com/in/rogerlin",
    "https://x.com/seoking_tw"
  ]
}
</script>


B. Organization JSON-LD（含 sameAs）
<script type="application/ld+json">
{
  "@context":"https://schema.org",
  "@type":"Organization",
  "@id":"https://www.seoking.com.tw/#org",
  "name":"柏瀚國際科技有限公司（SEO 優化王）",
  "url":"https://www.seoking.com.tw",
  "logo":"https://www.seoking.com.tw/assets/logo.png",
  "contactPoint":{
    "@type":"ContactPoint","telephone":"+886-2-2563-4727","contactType":"customer service","areaServed":"TW"
  },
  "sameAs":[
    "https://www.seoking.com.tw",
    "https://www.facebook.com/seoking.tw",
    "https://www.youtube.com/@seoking",
    "https://www.linkedin.com/company/seoking"
  ]
}
</script>


C. FAQPage JSON-LD（短答版）
<script type="application/ld+json">
{
  "@context":"https://schema.org",
  "@type":"FAQPage",
  "mainEntity":[
    {
      "@type":"Question",
      "name":"AI 搜尋優化與傳統 SEO 差在哪裡？",
      "acceptedAnswer":{"@type":"Answer","text":"AI 時代重在可抽取與可引用：問答結構、證據密度與同一實體標記是關鍵。"}
    }
  ]
}
</script>


D. 圖片 ImageObject JSON-LD
<script type="application/ld+json">
{
  "@context":"https://schema.org",
  "@type":"ImageObject",
  "contentUrl":"https://www.seoking.com.tw/assets/cards/aiso_claim_012_202509.png",
  "caption":"2024–2025 台灣 B2B AIO 指標提升 37%，來源：SEOKING 內部儀表板",
  "creator":{"@id":"https://www.seoking.com.tw/#org"},
  "datePublished":"2025-09-01",
  "license":"https://www.seoking.com.tw/license"
}
</script>


E. UTM 命名規範（CSV 欄位）
link_id, url, utm_source, utm_medium, utm_campaign, utm_content, ai_engine, ai_model, ai_surface, ai_session


________________


統一自動化藍圖（建議）
* n8n：內容上線 → 觸發 IndexNow + WebSub；同步更新 q_list.csv 狀態；推送短鏈。
* GitHub Actions：PR 合併即重建 JSON-LD、錨點檢查、死鏈掃描、圖卡 ALT 檢查。
* GTM/GA4：解析 UTM → 設置 ai_* 維度與 deep_link_open/ai_click 事件。
* Looker Studio：ACR/LAR × AI 來源分流 × GEO/VSO 命中率交叉儀表板。