<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="4240" viewBox="0 0 1400 4240" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="strategyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="preparationGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="operationGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="optimizationGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#c471f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fa71cd;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="deploymentGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#0000001A"/>
    </filter>
  </defs>
  
  <!-- 主標題 -->
  <rect width="1400" height="200" fill="url(#headerGradient)" filter="url(#shadow)"/>
  <rect x="20" y="20" width="1360" height="160" rx="15" fill="rgba(255,255,255,0.95)"/>
  <text x="700" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
        font-size="36" font-weight="bold" fill="#2d3748">🤖 AISO × NotebookLM 整合策略架構</text>
  <text x="700" y="105" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
        font-size="20" fill="#4a5568">AI 搜尋優化框架與 Google NotebookLM 的完整整合方案</text>
  <text x="700" y="135" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
        font-size="18" fill="rgba(255,255,255,0.9)">SEO優化王 × 柏瀚國際 AISO 360™ 戰略框架</text>
  <text x="700" y="160" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
        font-size="16" fill="rgba(255,255,255,0.9)">從策略規劃到實務操作的完整落地指南</text>

  <!-- 關鍵要點概述 -->
  <g transform="translate(50, 230)">
    <rect width="1300" height="150" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">🎯 整合策略關鍵要點</text>
    <text x="50" y="65" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• AISO 框架（AIO/GEO/VSO/AVO）直接對位 NotebookLM 工具，生成繁體中文內容</text>
    <text x="50" y="90" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• 穩定產生帶品牌實體的輸出（創辦人 Roger Lin、SEO 優化王、柏瀚國際科技）</text>
    <text x="50" y="115" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• 內容引用率可提升 20-30%，產製時間縮短 50%</text>
  </g>

  <!-- 1. 策略藍圖 -->
  <g transform="translate(50, 410)">
    <rect width="1300" height="650" rx="20" fill="url(#strategyGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣ 策略藍圖：AISO × NotebookLM 對位</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="18" fill="#4a5568">四個模組與 NotebookLM 功能對位，形成閉環內容生成系統</text>

    <!-- AISO 四模組對位 -->
    <g transform="translate(40, 100)">
      <!-- AIO 模組 -->
      <rect x="0" y="0" width="300" height="120" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="150" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">AIO（摘要可引用）</text>
      <text x="20" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">對位：NotebookLM Reports > AIO 摘要</text>
      <text x="20" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">策略：生成一屏式結論摘要</text>
      <text x="20" y="90" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">戰術：內嵌實體與數據，支持 SEO 引用</text>
      <text x="20" y="110" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">操作：貼入模板後輸出摘要作為 AIO 資產</text>

      <!-- GEO 模組 -->
      <rect x="320" y="0" width="300" height="120" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="470" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">GEO（生成問答）</text>
      <text x="340" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">對位：Reports &gt; Q&amp;A 生成</text>
      <text x="340" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">策略：產出結構化問答集</text>
      <text x="340" y="90" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">戰術：設計問題模板涵蓋品牌案例</text>
      <text x="340" y="110" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">操作：生成 10-20 組 Q&amp;A，用於網站 FAQ</text>

      <!-- VSO 模組 -->
      <rect x="640" y="0" width="300" height="120" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="790" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">VSO（語音命中）</text>
      <text x="660" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">對位：Studio > Voice Script</text>
      <text x="660" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">策略：優化語音腳本匹配語音搜尋</text>
      <text x="660" y="90" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">戰術：強調自然口語與品牌關鍵詞</text>
      <text x="660" y="110" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">操作：設定 AI 主持人重點，輸出 m4a 腳本</text>

      <!-- AVO 模組 -->
      <rect x="960" y="0" width="300" height="120" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="1110" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">AVO（Agent 可見度）</text>
      <text x="980" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">對位：整體輸出佈署</text>
      <text x="980" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">策略：提升 AI 代理對品牌辨識度</text>
      <text x="980" y="90" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">戰術：確保輸出包含 NAP 與網站連結</text>
      <text x="980" y="110" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">操作：後製視音頻上傳 YouTube/Spotify</text>
    </g>

    <!-- 預期效益 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="1220" height="180" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📊 預期效益與 KPI 指標</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#2d3748">內容產製效率：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 內容產製速度提升 40-60%（基於 Google AI Studio 使用者反饋）</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 品牌提及率達 90% 以上</text>
      
      <text x="30" y="130" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#2d3748">引用效果：</text>
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 內容引用率提升 20-30%（使用 Google Analytics 追蹤）</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 品牌曝光透過 Ahrefs 監測反向連結，平均 ROI 提升 25%</text>
    </g>

    <!-- 風險與建議 -->
    <g transform="translate(40, 440)">
      <rect x="0" y="0" width="1220" height="160" rx="12" fill="rgba(255, 154, 154, 0.15)" stroke="#ff9a9a" stroke-width="2"/>
      <text x="610" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">⚠️ 潛在挑戰與風險控制</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• AI 生成準確性：需注意 AI 幻覺（hallucination），可能需手動調整避免遺漏實體</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 品牌敏感議題：建議多輪檢核以維持專業性</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 依賴高品質來源檔案：確保上傳檔案準確率 >95%</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#4a5568">• 建議反向思考：模擬競爭對手使用，識別潛在漏洞</text>
    </g>
  </g>

  <!-- 2. 前置準備 -->
  <g transform="translate(50, 1090)">
    <rect width="1300" height="750" rx="20" fill="url(#preparationGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="720" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">2️⃣ 前置準備：一次設定，長期複用</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="18" fill="#4a5568">建立可重複使用的資產庫，設定後複用率達 80%</text>

    <!-- 上傳來源檔案 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="280" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📁 上傳來源檔案</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 訪談逐字稿 / 影片字幕</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">支援格式：.txt/.md/.vtt/.srt</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">建議：使用 Otter.ai 或 YouTube 自動字幕轉檔</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">確保逐字準確率 >95%</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. m4a 音訊、投影片 PDF、成效圖表</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">音訊：使用 Audacity 壓縮</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">PDF：確保可搜尋文字（非掃描圖）</text>
      <text x="30" y="210" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">圖表：加入水印品牌 logo，提升 AVO</text>
      
      <text x="30" y="240" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 品牌事實卡 brand_facts.md</text>
      <text x="30" y="260" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">強化實體與口吻，確保品牌一致性</text>
    </g>

    <!-- 品牌事實卡範例 -->
    <g transform="translate(640, 100)">
      <rect x="0" y="0" width="580" height="280" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📋 品牌事實卡範例內容</text>
      
      <text x="30" y="55" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#2d3748">brand_facts.md</text>
      <text x="30" y="75" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">品牌：柏瀚國際科技有限公司（品牌別名：SEO 優化王）</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">英文：SEOKING INTERNATIONAL TECHNOLOGY CO., LTD.</text>
      <text x="30" y="115" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">創辦人：Roger Lin（林成基，Roger Lin）</text>
      <text x="30" y="135" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">NAP：10458 台北市中山區新生北路二段31之1號9樓之7</text>
      <text x="30" y="155" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">電話：02-2563-4727 | 信箱：<EMAIL></text>
      <text x="30" y="175" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">網站：https://seoking.com.tw</text>
      <text x="30" y="195" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">定位：台灣頂尖 AI 搜尋優化（AISO 360™：AIO/GEO/VSO/AVO）</text>
      <text x="30" y="215" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">口吻：專業、務實、結論優先，數據與案例說話</text>
      <text x="30" y="235" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">【強制提及實體】：創始人 Roger、SEO 優化王、柏瀚國際科技</text>
      <text x="30" y="255" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">【數據優先】以實測 KPI、案例提升幅度、時間週期呈現</text>
    </g>

    <!-- 命名規範 -->
    <g transform="translate(40, 400)">
      <rect x="0" y="0" width="580" height="180" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🏷️ 命名規範（利於檢索與再利用）</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">格式範例：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• YYYYMMDD_case-notes_柏瀚國際-AI行銷轉型.md</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• audio_訪談_20250827.m4a</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• slides_ROI-提升與前後測.pdf</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">使用前綴分類（如 "audio_"、"slides_"），便於進階搜尋</text>
    </g>

    <!-- 操作流程 -->
    <g transform="translate(640, 400)">
      <rect x="0" y="0" width="580" height="180" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">⚙️ 操作流程</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 建立專屬 Notebook</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">如 "AISO-Content-Generator"</text>
      
      <text x="30" y="105" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. 上傳所有檔案</text>
      <text x="30" y="125" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">目標 5-10 個來源</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">3. 測試品牌實體出現率</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">若低於 80%，調整 brand_facts.md</text>
    </g>

    <!-- 補充建議 -->
    <g transform="translate(40, 600)">
      <rect x="0" y="0" width="1220" height="100" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="610" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💡 補充建議與最佳實踐</text>
      
      <text x="30" y="55" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• 若檔案超過 50MB，分割上傳；加入時間戳記（如 [00:01:23]）以利後續引用</text>
      <text x="30" y="75" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• 圖表含數據時，標註來源（如 "ROI 提升 35%，基於 2024 Q2 實測"）</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• 新增「同義詞/暱稱」如 "Roger Lin"、"SEOKING"，並羅馬拼寫以防 AI 誤認</text>
    </g>
  </g>

  <!-- 3. Studio 操作 -->
  <g transform="translate(50, 1870)">
    <rect width="1300" height="650" rx="20" fill="url(#operationGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">3️⃣ Studio 操作：繁中視音頻一次到位</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="18" fill="#4a5568">利用 Studio 的 AI 主持人生成視音頻，確保繁體中文輸出</text>

    <!-- AI 主持人設定 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🎙️ AI 主持人重點設定</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">必須逐一朗讀模板：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">「【必須逐一朗讀】創始人 Roger（Roger Lin）、</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">SEO 優化王、柏瀚國際科技有限公司、柏瀚國際。」</text>
      
      <text x="30" y="130" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">情境設定：</text>
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">「以專業口吻討論 AI 行銷轉型，引用實測數據」</text>
      
      <text x="30" y="180" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">內容結構：</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">「開頭介紹品牌、結尾呼籲行動」</text>
      <text x="30" y="220" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">預期：單次操作產出 5-10 分鐘腳本</text>
    </g>

    <!-- 操作流程 -->
    <g transform="translate(640, 100)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(254, 225, 64, 0.15)" stroke="#fee140" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">⚙️ 操作流程步驟</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">1. Studio > Video/Audio Overview</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">選擇視頻或音頻模式</text>
      
      <text x="30" y="105" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">2. Host Focus > 貼入文字</text>
      <text x="30" y="125" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">貼入品牌實體強制提及模板</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">3. Generate > 生成內容</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">選擇 "Deep Dive" 模式獲取更詳細腳本</text>
      
      <text x="30" y="195" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">4. 下載 m4a/mp4</text>
      <text x="30" y="215" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">輸出後下載音頻或視頻檔案</text>
    </g>

    <!-- 實體穩定技巧 -->
    <g transform="translate(40, 360)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🔧 實體穩定技巧</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">檢查與修正：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">1. 檢查輸出，若遺漏品牌實體</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">2. 編輯 Host Focus 加強制提及</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">3. 生成 2-3 版本比較</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">語言設定：</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">確保 NotebookLM 介面為繁體中文</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">（帳戶設定 > 語言）</text>
      
      <text x="30" y="220" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">若 AI 混用，後製以 Descript 編輯</text>
    </g>

    <!-- 繁體中文最佳化 -->
    <g transform="translate(640, 360)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🇹🇼 繁體中文最佳化細節</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">確保繁體輸出：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">Host Focus 加 "使用台灣繁體中文"</text>
      
      <text x="30" y="105" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">字詞檢查：</text>
      <text x="30" y="125" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">檢查字詞如 "優化"（非 "优化"）</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">音頻速率：</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">150-160 wpm 適合 VSO</text>
      
      <text x="30" y="195" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">品牌口吻治理：</text>
      <text x="30" y="215" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">貼入 Note："專業、務實、結論優先，數據說話"</text>
    </g>
  </g>

  <!-- 4. Reports 與內容再利用 -->
  <g transform="translate(50, 2550)">
    <rect width="1300" height="550" rx="20" fill="url(#optimizationGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="520" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">4️⃣ Reports 與內容再利用：GEO/VSO 產能化</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="18" fill="#4a5568">轉化 Reports 為 GEO/VSO 資產，產出可規模化的 Q&amp;A 與腳本</text>

    <!-- Q&amp;A 生成 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="380" height="180" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="190" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📝 Q&amp;A（GEO 用）</text>
      
      <text x="20" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">操作：</text>
      <text x="20" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">Reports &gt; Q&amp;A &gt; Generate</text>
      
      <text x="20" y="95" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">輸出：</text>
      <text x="20" y="115" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">生成 10-15 組問答</text>
      <text x="20" y="135" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">導出為 .md，用於網站 GEO</text>
      
      <text x="20" y="160" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">目標：提升生成式引擎抓取率</text>
    </g>

    <!-- Voice Script -->
    <g transform="translate(440, 100)">
      <rect x="0" y="0" width="380" height="180" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="190" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎤 Voice Script（VSO 用）</text>
      
      <text x="20" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">操作：</text>
      <text x="20" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">從 Studio 導出腳本</text>
      
      <text x="20" y="95" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">優化：</text>
      <text x="20" y="115" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">加入停頓標記（如 [pause 2s]）</text>
      <text x="20" y="135" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">轉 ElevenLabs 等工具合成語音</text>
      
      <text x="20" y="160" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">目標：語音搜尋優化</text>
    </g>

    <!-- AIO 摘要 -->
    <g transform="translate(840, 100)">
      <rect x="0" y="0" width="380" height="180" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="190" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 AIO 摘要（可被引用）</text>
      
      <text x="20" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">操作：</text>
      <text x="20" y="70" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">Reports > Summary</text>
      
      <text x="20" y="95" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">要求：</text>
      <text x="20" y="115" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">確保含 NAP 與網站連結</text>
      <text x="20" y="135" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">生成簡潔摘要</text>
      
      <text x="20" y="160" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">目標：一屏結論，提升引用</text>
    </g>

    <!-- 再利用建議 -->
    <g transform="translate(40, 300)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🔄 內容再利用與自動化建議</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• 合併多 Notebook 輸出：整合不同專案的內容資產</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• 使用 Zapier 自動化導出至 Google Drive：建立自動化工作流程</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">• 進階整合：API 如 Google Cloud 自動化，初次測試 30 分鐘</text>
    </g>

    <!-- 最短路徑操作 -->
    <g transform="translate(40, 440)">
      <rect x="0" y="0" width="1220" height="80" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚡ 最短路徑操作（4 步驟）</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">1. 建立 Notebook，上傳 3 類來源與 brand_facts.md → 2. Studio 貼模板生成視音頻 → 3. Reports 產 Q&amp;A/摘要 → 4. 佈署並檢核 KPI</text>
    </g>
  </g>

  <!-- 5. 出片後佈署與 QA 檢核 -->
  <g transform="translate(50, 3130)">
    <rect width="1300" height="650" rx="20" fill="url(#deploymentGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">5️⃣ 出片後佈署與 QA 檢核</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="18" fill="#4a5568">AVO 可引用度提升與品質控制機制</text>

    <!-- 佈署策略 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🚀 AVO 佈署策略</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">YouTube 佈署：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">標題：「AISO 360™ 實務案例 - SEO 優化王」</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">嵌入網站連結，提升 AVO</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">Spotify 佈署：</text>
      <text x="30" y="145" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">音頻內容上傳至 Podcast 平台</text>
      
      <text x="30" y="170" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">Schema.org 標記：</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">使用結構化數據標記摘要，增加引用率</text>
    </g>

    <!-- QA 檢核清單 -->
    <g transform="translate(640, 100)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="30" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">✅ QA 檢核清單</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">品牌實體檢查：</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">□ 創始人 Roger 提及</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">□ SEO 優化王 品牌名稱</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">□ 柏瀚國際科技 公司全名</text>
      
      <text x="30" y="145" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">內容品質：</text>
      <text x="30" y="165" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">□ 繁體中文正確性</text>
      <text x="30" y="185" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">□ 專業術語準確性</text>
      <text x="30" y="205" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="16" fill="#666">□ 數據來源標註</text>
    </g>

    <!-- KPI 指標追蹤 -->
    <g transform="translate(40, 340)">
      <rect x="0" y="0" width="1220" height="260" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="610" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📈 KPI 指標追蹤與效益評估</text>
      
      <!-- KPI 表格 -->
      <g transform="translate(30, 50)">
        <!-- 表格標題行 -->
        <rect x="0" y="0" width="200" height="30" fill="#4facfe" stroke="#4facfe" stroke-width="1"/>
        <text x="100" y="20" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="white">模組</text>
        
        <rect x="200" y="0" width="300" height="30" fill="#4facfe" stroke="#4facfe" stroke-width="1"/>
        <text x="350" y="20" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="white">預期效益</text>
        
        <rect x="500" y="0" width="300" height="30" fill="#4facfe" stroke="#4facfe" stroke-width="1"/>
        <text x="650" y="20" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="white">實測案例</text>
        
        <rect x="800" y="0" width="360" height="30" fill="#4facfe" stroke="#4facfe" stroke-width="1"/>
        <text x="980" y="20" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="white">KPI 指標</text>
        
        <!-- AIO 行 -->
        <rect x="0" y="30" width="200" height="40" fill="rgba(67, 233, 123, 0.1)" stroke="#43e97b" stroke-width="1"/>
        <text x="100" y="50" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="#2d3748">AIO</text>
        
        <rect x="200" y="30" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="210" y="50" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">一屏摘要，提升引用</text>
        
        <rect x="500" y="30" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="510" y="50" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">HubSpot 報告：引用率 +25%</text>
        
        <rect x="800" y="30" width="360" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="810" y="50" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">摘要長度 &lt;300 字，實體提及 100%</text>
        
        <!-- GEO 行 -->
        <rect x="0" y="70" width="200" height="40" fill="rgba(79, 172, 254, 0.1)" stroke="#4facfe" stroke-width="1"/>
        <text x="100" y="90" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="#2d3748">GEO</text>
        
        <rect x="200" y="70" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="210" y="90" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">生成 Q&amp;A，SEO 流量 +30%</text>
        
        <rect x="500" y="70" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="510" y="90" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">Ahrefs 數據：FAQ 頁面排名提升</text>
        
        <rect x="800" y="70" width="360" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="810" y="90" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">Q&amp;A 組數 10+，問題多樣性</text>
        
        <!-- VSO 行 -->
        <rect x="0" y="110" width="200" height="40" fill="rgba(250, 112, 154, 0.1)" stroke="#fa709a" stroke-width="1"/>
        <text x="100" y="130" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="#2d3748">VSO</text>
        
        <rect x="200" y="110" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="210" y="130" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">語音腳本，語音搜尋命中 +40%</text>
        
        <rect x="500" y="110" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="510" y="130" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">Voicebot 研究：自然語言優化</text>
        
        <rect x="800" y="110" width="360" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="810" y="130" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">腳本長度 5-10 分，停頓率 20%</text>
        
        <!-- AVO 行 -->
        <rect x="0" y="150" width="200" height="40" fill="rgba(196, 113, 245, 0.1)" stroke="#c471f5" stroke-width="1"/>
        <text x="100" y="170" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" font-weight="bold" fill="#2d3748">AVO</text>
        
        <rect x="200" y="150" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="210" y="170" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">代理可見度，曝光 +35%</text>
        
        <rect x="500" y="150" width="300" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="510" y="170" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">Google Analytics：反向連結增長</text>
        
        <rect x="800" y="150" width="360" height="40" fill="rgba(255,255,255,0.9)" stroke="#dee2e6" stroke-width="1"/>
        <text x="810" y="170" font-family="'Microsoft JhengHei', sans-serif" 
              font-size="16" fill="#666">上傳平台 3+，觀看次數/月</text>
      </g>
    </g>
  </g>

  <!-- 實施建議與總結 -->
  <g transform="translate(50, 3810)">
    <rect width="1300" height="200" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">💡 實施建議與成功關鍵</text>
    
    <text x="50" y="70" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• 從前置準備開始，一次設定可長期複用，操作時優先使用 Host Focus 強化品牌口吻</text>
    <text x="50" y="95" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• 整體流程可縮短內容產製時間 50%，但在敏感品牌議題上，建議多輪檢核以維持專業性</text>
    <text x="50" y="120" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• 定期評估 KPI 如內容引用率（使用 Google Analytics 追蹤）和品牌曝光（透過 Ahrefs 監測反向連結）</text>
    <text x="50" y="145" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">• 若需客製化，建議迭代測試，確保 AI 生成內容與品牌調性一致</text>
    <text x="50" y="170" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">此手冊基於 NotebookLM 官方指南與 AI 行銷實務，確保落地性與可操作性</text>
  </g>

  <!-- 頁尾公司資訊 -->
  <g transform="translate(0, 4060)">
    <rect width="1400" height="160" fill="url(#headerGradient)"/>
    <rect x="50" y="20" width="1300" height="120" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 公司 Logo 區域 -->
    <circle cx="150" cy="80" r="35" fill="#667eea"/>
    <text x="150" y="88" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="24" font-weight="bold" fill="white">SEO</text>
    
    <!-- 公司資訊 -->
    <text x="220" y="55" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="20" font-weight="bold" fill="#2d3748">柏瀚國際科技有限公司 × SEO優化王</text>
    <text x="220" y="78" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">SEOKING INTERNATIONAL TECHNOLOGY CO., LTD.</text>
    <text x="220" y="98" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">創辦人：Roger Lin（林成基） | 台灣頂尖 AI 搜尋優化 AISO 360™</text>
    <text x="220" y="118" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#4a5568">📍 台北市中山區新生北路二段31之1號9樓之7 | 📞 02-2563-4727 | 🌐 https://seoking.com.tw</text>
    
    <!-- 版權資訊 -->
    <text x="1200" y="80" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#667eea">© 2025 AISO 360™</text>
    <text x="1200" y="100" text-anchor="middle" font-family="'Microsoft JhengHei', sans-serif" 
          font-size="16" fill="#667eea">All Rights Reserved</text>
  </g>

  <!-- 浮水印 -->
  <text x="1350" y="4220" text-anchor="end" font-family="'Microsoft JhengHei', sans-serif" 
        font-size="12" fill="rgba(102, 126, 234, 0.3)">Generated by AISO 360™ Framework</text>
</svg>