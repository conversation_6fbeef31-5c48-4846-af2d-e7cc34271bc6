社群 → AI 拉動 十大關鍵戰術的端到端作戰藍圖：以「AI Citation Impact × 可落地性」為主排序，逐項提供商業目的、產出規格、欄位/Schema 標準、SOP、KPI 與自動化鉤子。可直接納入你的 AISO 360™ 流程與 n8n/CI 產線。
________________


0) 總覽—治理與度量框架
* 目標函數：AI Citation Impact（被 AI 助理引用 × 正確歸因 × 可行動） × 可落地性（人/流程/工具成熟度）。
* 通用 Gate（每條戰術都要過）
   1. 可機器抽取：結構化（JSON-LD/CSV/HTML microdata）
   2. 可驗證：來源齊備（2–3 條權威來源或原始數據）
   3. 可追版：Canonical + lastmod + ETag + changelog
   4. 可執行：提供行動入口（Action JSON-LD/EntryPoint/API）
   5. 可聲讀：25–40 字直接答案 + speakable 區塊
* 核心 KPI 對齊：ACR（Assistant Citation Rate）、GEO_COV（問題覆蓋）、VSO_HIT（語音命中）、LAR（無鏈接歸因）、EVID_DENS（證據密度）、CS_SHARE（可引用片段占比）。
________________


1) 聲明頁（Source-of-Truth / Canonical + lastmod + ETag + changelog）
商業目的：讓 AI/代理把此頁視為唯一可信來源，一切主張與數據的權威錨點。
必備產出：/source-of-truth/<topic>/index.html（或 Markdown 產靜態頁），內含版本紀錄與 JSON-LD。
前端（Front-matter / Header）範例
---
title: "AISO 360™ 指標與定義 — Source of Truth"
canonical: "https://www.seoking.com.tw/aiso/source-of-truth"
lastmod: "2025-09-01T10:00:00+08:00"
version: "v1.3.2"
etag: "W/\"aiso-sot-v1.3.2-5f9a1c\""
changelog:
  - date: 2025-09-01
    ver: v1.3.2
    change: "新增 LAR 度量與 DataSet 連結"
  - date: 2025-08-20
    ver: v1.3.1
    change: "修訂 VSO HIT 檢測方法"
---


HTTP/Edge 設定（示意）
* ETag：以正文（不含導航/註腳）內容 hash 產生
* Cache-Control：max-age=600, stale-while-revalidate=86400
* Link headers：rel="canonical", rel="preload"（必要資源）
Article JSON-LD（含版本/維護者）
{
  "@context": "https://schema.org",
  "@type": "TechArticle",
  "@id": "https://www.seoking.com.tw/aiso/source-of-truth#article",
  "headline": "AISO 360™ 指標與定義 — Source of Truth",
  "url": "https://www.seoking.com.tw/aiso/source-of-truth",
  "datePublished": "2025-07-15",
  "dateModified": "2025-09-01",
  "version": "1.3.2",
  "mainEntityOfPage": "https://www.seoking.com.tw/aiso/source-of-truth",
  "maintainer": { "@type":"Organization", "name":"SEO 優化王", "url":"https://seoking.com.tw" },
  "identifier": "W/\"aiso-sot-v1.3.2-5f9a1c\""
}


SOP（5 步）
1. 以 Markdown 撰稿 → 產靜態頁（CI 內建校驗）。
2. 生成 ETag（正文 hash）→ 寫入 Front-matter 與 Response Header。
3. 更新 lastmod/version/changelog。
4. 驗證 Canonical、JSON-LD、無障礙（ARIA/LH）。
5. 發佈後以腳本 Ping IndexNow/Bing 與 Realtime declare page（X/Grok 用）。
KPI：ACR 提升、LAR>0.6、被同站內 100% 主張 sameAs 或 isBasedOn 指回此頁。
自動化：GitHub Actions：變更偵測→Lint→Schema 檢查→自動產 ETag→發佈→Ping。
________________


2) Citation Snippet 卡（H2→三點要旨→25–40 字→2–3 來源）
目的：給 AI/社群秒抽的可引用段。
內容規範
* H2：語意清晰（含關鍵詞）
* 三點要旨：每點 ≤18 字
* 引句：25–40 字，可單獨被朗讀
* 來源：2–3 條（學術/官方/自家 SoT）
欄位規格（YAML）
snippet:
  h2: "AIO 對 AI Citation 的三大槓桿"
  bullets: ["結構化摘要", "權威來源", "可行動入口"]
  teaser_25_40: "以結構化摘要 + 權威來源，讓 AI 可快速引用且可追溯。"
  sources:
    - name: "SoT"
      url: "https://www.seoking.com.tw/aiso/source-of-truth"
    - name: "Google Dev"
      url: "https://developers.google.com/search"


SOP（3 步）：擬題 → 填 YAML → 產出 HTML 卡片（共用模板，插入頁面/社群版位）。
KPI：CS_SHARE≥0.5、該段被 AIO/GEO 測試命中率≥70%。
自動化：n8n 每週掃描卡片 → 測試 50 題 Q-list 命中 → 產出報表。
________________


3) AIO Block（15 秒三欄摘要：定義 / 流程 / 數據）
目的：15 秒讓 AI/人抓重點。
HTML 模組（可複用）
<section class="aio-block" aria-label="15秒摘要">
  <div class="col"><h3>定義</h3><p>以結構化與證據導向，讓 AI 易於引用。</p></div>
  <div class="col"><h3>流程</h3><ol><li>收證</li><li>結構化</li><li>發佈</li></ol></div>
  <div class="col"><h3>數據</h3><p>ACR +35%，LAR 0.62→0.78（近 30 日）</p></div>
</section>


SOP：撰寫 → 15 秒朗讀檢測（TTS）→ 壓縮至 60–80 字/欄 → 上線。
KPI：AIO 區塊的引用段落占比、停留時間、GEO 命中加權。
自動化：CI 內建字數/可讀性檢查 + Lighthouse/INP 閾值門檻。
________________


4) Micro-Dataset（每篇附 CSV/JSON 可下載）
目的：可機器再利用的最小數據切片，提升 LLM 可引性與再引用。
CSV 欄位最小集
id, metric, value, unit, source_url, observed_at, license


SOP：從正文抽 3–5 個可量化指標 → 產 CSV/JSON → 以 DataDownload 挂載（見 #5）。
KPI：被外部引用次數、Assistant 生成答案中引用數據比例。
自動化：CSV Schema 檢核（列頭/單位/時間戳）→ 發佈即產校驗報告。
________________


5) DataSet JSON-LD（含 measurementTechnique / temporalCoverage / license）
目的：讓 AI 明確理解數據範圍、方法與授權。
JSON-LD 模板
{
  "@context": "https://schema.org",
  "@type": "Dataset",
  "@id": "https://www.seoking.com.tw/datasets/aiso-metrics-2025#dataset",
  "name": "AISO 360™ 指標樣本集",
  "description": "示例數據：ACR、LAR、VSO_HIT、GEO_COV 的週度樣本。",
  "url": "https://www.seoking.com.tw/datasets/aiso-metrics-2025",
  "creator": { "@type":"Organization", "name":"SEO 優化王", "url":"https://seoking.com.tw" },
  "measurementTechnique": ["ACR weekly test via model suite", "Voice hit simulated Q-list"],
  "temporalCoverage": "2025-06/2025-09",
  "license": "https://creativecommons.org/licenses/by/4.0/",
  "variableMeasured": [
    { "@type":"PropertyValue", "name":"ACR", "unitCode":"P1", "description":"Assistant Citation Rate" },
    { "@type":"PropertyValue", "name":"LAR", "unitCode":"P1" }
  ],
  "distribution": [
    {
      "@type": "DataDownload",
      "encodingFormat": "text/csv",
      "contentUrl": "https://www.seoking.com.tw/datasets/aiso-metrics-2025.csv"
    },
    {
      "@type": "DataDownload",
      "encodingFormat": "application/json",
      "contentUrl": "https://www.seoking.com.tw/datasets/aiso-metrics-2025.json"
    }
  ]
}


KPI：外部再利用次數、ACR/LAR 變化對應使用的 dataset 版本。
自動化：每次更新 Micro-Dataset → 自動 bump Dataset temporalCoverage 與版本。
________________


6) ClaimReview（可驗證主張卡）
目的：釘牢可驗證主張，提升信任與引用可用性。
內容規範：單卡一主張、評級理由、證據鏈（SoT + 第三方）。
JSON-LD 模板
{
  "@context": "https://schema.org",
  "@type": "ClaimReview",
  "url": "https://www.seoking.com.tw/claims/aiso-raises-acr",
  "datePublished": "2025-09-01",
  "claimReviewed": "採用 AISO 360™ 後，ACR 平均提升 20–35%。",
  "author": { "@type": "Organization", "name": "SEO 優化王" },
  "reviewRating": {
    "@type": "Rating",
    "ratingValue": "4",
    "bestRating": "5",
    "alternateName": "大致正確"
  },
  "itemReviewed": {
    "@type": "CreativeWork",
    "@id": "https://www.seoking.com.tw/aiso/source-of-truth#article"
  }
}


KPI：被 Assistant 作為判斷依據引用的比例、負面錯配降低。
自動化：評級標準化（enum）、證據鏈自動校驗（來源 200/可存取/非臨時）。
________________


7) Tool Card / Action JSON-LD（可執行步驟與 EntryPoint）
目的：把內容升級為可被代理直接調用的行動。
Action JSON-LD 模板（示例：搬家報價計算）
{
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "Moving Quote Calculator",
  "operatingSystem": "Web",
  "applicationCategory": "BusinessApplication",
  "offers": { "@type": "Offer", "price": "0", "priceCurrency": "TWD" },
  "potentialAction": {
    "@type": "Action",
    "name": "GetMovingQuote",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://api.example.com/quote?origin={origin}&dest={dest}&size={size}",
      "actionPlatform": [
        "https://schema.org/DesktopWebPlatform",
        "https://schema.org/IntegratedApplication"
      ],
      "httpMethod": "GET"
    },
    "result": { "@type": "Thing", "name": "Quote" }
  }
}


KPI：Agent 調用次數、成功回應率、轉換。
自動化：OpenAPI→自動生成 Action JSON-LD；n8n/webhook 作為後端 Adapter。
________________


8) Speakable Pair（至少兩段可朗讀）
目的：強化語音/音頻摘要的命中與可讀性。
規範：每段 25–40 字；避免第一人稱；用「問題→直球答案」結構。
Article + Speakable JSON-LD
{
  "@context": "https://schema.org",
  "@type": "Article",
  "@id": "https://www.seoking.com.tw/aiso/faq#article",
  "headline": "AISO 360™ 常見問題",
  "speakable": {
    "@type": "SpeakableSpecification",
    "cssSelector": ["#speakable-1", "#speakable-2"]
  }
}


KPI：VSO_HIT≥0.6；朗讀平均長度 20–30 秒。
自動化：TTS 审核（長度/可懂度）→ 低分自動回推編輯。
________________


9) ImageObject 標記（creator / datePublished / caption / license）
目的：影像可被 AI 正確歸因與安全重用。
JSON-LD 模板
{
  "@context": "https://schema.org",
  "@type": "ImageObject",
  "contentUrl": "https://www.seoking.com.tw/images/aiso-framework.png",
  "creator": { "@type":"Organization", "name":"SEO 優化王" },
  "datePublished": "2025-09-01",
  "caption": "AISO 360™ 框架視覺圖",
  "license": "https://www.seoking.com.tw/license#cc-by-4"
}


作業細節：嵌入 IPTC/EXIF（作者、版權、說明）；圖片檔名語意化；WebP + AVIF。
KPI：以圖搜/AI 生成引用時的正確署名率。
自動化：發佈前自動寫 IPTC；壓縮尺寸與格式轉換流水線。
________________


10) VideoObject＋逐字稿＋時間碼＋Speakable
目的：影音內容被 AI 精準引用、片段定位、語音回覆友好。
JSON-LD 模板（含章節 Clip 與逐字稿 URL）
{
  "@context": "https://schema.org",
  "@type": "VideoObject",
  "name": "AISO 360™ 操作總覽（3 分鐘）",
  "uploadDate": "2025-09-01",
  "contentUrl": "https://cdn.example.com/aiso-overview.mp4",
  "thumbnailUrl": "https://www.seoking.com.tw/images/aiso-thumb.jpg",
  "transcript": "https://www.seoking.com.tw/transcripts/aiso-overview.vtt",
  "hasPart": [
    { "@type": "Clip", "name": "定義", "startOffset": 0, "endOffset": 45 },
    { "@type": "Clip", "name": "流程", "startOffset": 46, "endOffset": 110 }
  ],
  "isPartOf": { "@id": "https://www.seoking.com.tw/aiso/source-of-truth#article" }
}


作業細節：提供 SRT/VTT（時間碼）、章節錨點（#t=60）、對應 Speakable Pair。
KPI：被 Assistant 引用之片段級命中、平均觀看完成度。
自動化：上傳後自動生成 VTT（ASR）→ 人工微調 → 重新發佈 & 更新 JSON-LD。
________________


11) 交叉關聯（@graph 建議）
將 Article（SoT） / Dataset / ClaimReview / VideoObject / ImageObject / SoftwareApplication 以 @id 互相串連，提升「機器可理解的證據網」。
示意
{
  "@context": "https://schema.org",
  "@graph": [
    { "@id":"https://.../source-of-truth#article", "@type":"TechArticle" },
    { "@id":"https://.../datasets/aiso#dataset", "@type":"Dataset", "isBasedOn":"https://.../source-of-truth#article" },
    { "@type":"ClaimReview", "itemReviewed": { "@id":"https://.../source-of-truth#article" } },
    { "@type":"VideoObject", "isPartOf": { "@id":"https://.../source-of-truth#article" } },
    { "@type":"ImageObject", "about": { "@id":"https://.../source-of-truth#article" } },
    { "@type":"SoftwareApplication", "potentialAction": { "@type":"Action" } }
  ]
}


________________


12) 作業節奏（Runbook）
* 週更節奏：
   * 週一：SoT 更新 + 變更審核
   * 週三：發 2 張 Citation Snippet 卡 + 1 個 AIO Block
   * 週五：Micro-Dataset 滾動更新 + Dataset JSON-LD 版本提升
* 月度：新增 2 條 ClaimReview；1 支 90–180 秒 VideoObject（附逐字稿/章節）。
* 例行檢測：GEO Q-list（50 題×14 天）、TTS 可讀性、Schema 校驗（structured-data-testing）。
________________


13) 監測儀表（最小欄位集）
ai_citation_dashboard.csv（可導入 Looker Studio）
date, asset_id, asset_type, test_suite, hit_rate, position, sentiment, attribution_ok, action_invoked, source_engine, notes


* asset_type：SoT | Snippet | AIO | Dataset | ClaimReview | Video | Image | Tool
* test_suite：GEO_Q50 | VSO_TTS | ACR_Scan | LAR_Check
* attribution_ok：true/false（是否正確指向 SoT/品牌）
________________


14) 交付包目錄（v1.0 建議）
/delivery/
  source-of-truth/
    index.md
    article.jsonld
  snippets/
    *.yaml
  aio-blocks/
    *.html
  datasets/
    aiso-metrics-2025.csv
    aiso-metrics-2025.json
    dataset.jsonld
  claims/
    *.jsonld
  actions/
    toolcard.jsonld
    openapi.yaml
  media/
    images/*.jsonld
    videos/*.jsonld
  dashboards/
    ai_citation_dashboard.csv


________________


15) 風險控管與最佳化建議
* 一致性風險：頁面與 Dataset 指標命名不一致 → 建立「Data Dictionary」與 enum 檢查。
* 來源可用性：外部來源失效 → n8n 定時 URL 健康檢查（4XX/5XX 自動改用備援來源）。
* 授權風險：圖片/數據授權不明 → 以 License 欄位與頁尾 License 區塊雙重標註。
* 延遲風險：多模組上線節奏失衡 → 以 SoT 為唯一主檔，其他模組 CI 依賴 SoT 的 tag 版本。
________________


一鍵落地（行動清單）
1. 建 SoT 模板（Front-matter + Article JSON-LD + Changelog 區塊）。
2. 建 Snippet YAML 與 AIO Block 通用組件（CI 字數/可讀性校驗）。
3. 為每篇內容抽 Micro-Dataset + Dataset JSON-LD。
4. 兩週內完成 2 條 ClaimReview；每月 1 個 Action（Tool Card）。
5. 全站媒體補齊 Image/Video JSON-LD + 逐字稿/時間碼。
6. 接上 n8n：Q-list 自動測試 → 寫回 ai_citation_dashboard.csv。