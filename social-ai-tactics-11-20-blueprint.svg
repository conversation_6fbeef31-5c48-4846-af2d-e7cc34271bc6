<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="8500" viewBox="0 0 1400 8500" xmlns="http://www.w3.org/2000/svg" style="display: block; margin: 0 auto; max-width: 100%; height: auto;">
  <defs>
    <!-- 漸層定義 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="authorGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="entityGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="qlistGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="faqGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#c471f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fa71cd;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="evidenceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    
    <!-- 陰影效果 -->
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#0000001A"/>
    </filter>
  </defs>

  <!-- 主標題區域 -->
  <g>
    <rect width="1400" height="200" fill="url(#headerGradient)" filter="url(#shadow)"/>
    <rect x="20" y="20" width="1360" height="160" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 主標題 -->
    <text x="700" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="36" font-weight="bold" fill="#2d3748">⚔️ 社群→AI 拉動 關鍵戰術 11–20 作戰藍圖</text>
    <text x="700" y="105" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="20" fill="#4a5568">策略目標 → 交付物規格 → 操作SOP → QA檢核 → KPI/儀表板 五段式落地方案</text>
    <text x="700" y="135" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="rgba(255,255,255,0.9)">SEO優化王 × 柏瀚國際 AISO 360™ 戰略框架</text>
    <text x="700" y="160" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="rgba(255,255,255,0.9)">以 AISO 360™ 工具鏈（n8n/GTM/GA4/Repo/Notion）為預設環境</text>
  </g>  <!-- 框架
總覽 -->
  <g transform="translate(50, 230)">
    <rect width="1300" height="150" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">🎯 戰術藍圖總覽</text>
    <text x="650" y="65" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">十大關鍵戰術：從作者身份建立到 AI 流量分析的完整閉環</text>
    <text x="650" y="90" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">強化 EEAT 與可歸因能力，提升 AI 助理引用率與品牌可見度</text>
    <text x="650" y="115" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#667eea">每個戰術都包含：策略目標 → 交付物規格 → 操作SOP → QA檢核 → KPI儀表板</text>
  </g>

  <!-- 11. Author Graph 對齊 -->
  <g transform="translate(50, 410)">
    <rect width="1300" height="750" rx="20" fill="url(#authorGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="720" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣1️⃣ Author Graph 對齊（作者名片＋Author Schema）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">把「人（作者）—內容—組織」三者關係機器可讀化，強化 EEAT 與可歸因能力</text>

    <!-- 交付物規格 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="200" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">author_profile.md：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">姓名｜職銜｜一行定位｜3-5條資歷</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">代表作｜聯繫方式｜授權宣告</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">person.jsonld（每位作者 1 檔）：</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">@type: Person，worksFor（Organization）</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">jobTitle、knowsAbout、sameAs[]、url</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">image、alumniOf、award、hasCredential</text>

      <!-- 操作SOP -->
      <rect x="600" y="0" width="580" height="200" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 盤點作者：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">建立 author_registry.csv</text>
      <text x="630" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（author_id、display_name、lang、job_title）</text>
      
      <text x="630" y="115" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. Git 版本化作者檔：</text>
      <text x="630" y="135" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">/data/authors/{author_id}.json</text>
      
      <text x="630" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 文章模版統一 byline：</text>
      <text x="630" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">author_id → 客製名片 + Person JSON-LD</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 320)">
      <rect x="0" y="0" width="580" height="180" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• Rich Results/Schema 驗證無錯</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• byline 顯示一致</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 同一作者名稱與拼寫全站唯一</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 文章頁的 datePublished/modified</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">  與作者更新紀錄一致</text>

      <rect x="600" y="0" width="580" height="180" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">作者署名覆蓋率 ≥ 95%</text>
      <text x="630" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">含作者 JSON-LD 的頁面比重 ≥ 90%</text>
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">作者名 + 品牌名的聯合提及</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">在 AI 回答中的準確率（LAR）↑</text>
    </g>

    <!-- Person JSON-LD 範例 -->
    <g transform="translate(40, 520)">
      <rect x="0" y="0" width="1220" height="180" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 Person JSON-LD 範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">{</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@context":"https://schema.org",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "@type":"Person",</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "name":"Roger Lin（林成基）",</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "jobTitle":"創辦人｜AI 搜尋優化顧問",</text>
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "worksFor":{"@id":"https://www.seoking.com.tw/#org"},</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "knowsAbout":["AI SEO","AIO/GEO/VSO/AVO","EEAT"]</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "url":"https://www.seoking.com.tw/about/roger-lin",</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "image":"https://www.seoking.com.tw/assets/roger.jpg",</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "sameAs":[</text>
      <text x="650" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://www.linkedin.com/in/rogerlin",</text>
      <text x="650" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://x.com/seoking_tw"</text>
      <text x="650" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  ]</text>
      <text x="650" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">}</text>
    </g>
  </g>  
<!-- 12. Entity sameAs -->
  <g transform="translate(50, 1190)">
    <rect width="1300" height="650" rx="20" fill="url(#entityGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣2️⃣ Entity sameAs（品牌/作者/產品繫結）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">統一實體身分，降低同名歧義，提升模型對「你=這個品牌/人/產品」的信心</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">sameas_registry.csv：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">entity_id、entity_type</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（Organization/Person/Product/Service）</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">canonical_url、sameAs[]（權重排序）</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">權重排序：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">官網→政府/工商→Wiki/DB</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">→主流社媒→影音/程式碼庫</text>

      <rect x="600" y="0" width="580" height="220" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 列出每個實體的「權威頁」與「社群頁」</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">死鏈/跳轉一律清掉</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 只保留可長期穩定 200 的 URL</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">避免短鏈當 sameAs</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 在官網全站範本注入</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Organization/Person/Product 的 sameAs</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 340)">
      <rect x="0" y="0" width="580" height="140" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• sameAs 不混用追蹤參數</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 不指向重定向頁</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• entity_id 與 schema @id 對齊</text>

      <rect x="600" y="0" width="580" height="140" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">實體覆蓋率 ≥ 95%</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（有 sameAs 的實體/總實體）</text>
      <text x="630" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">AI 回答中品牌/作者對應外部資料頁</text>
      <text x="630" y="115" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">的一致率↑</text>
    </g>

    <!-- Organization JSON-LD 範例 -->
    <g transform="translate(40, 500)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 Organization JSON-LD（含 sameAs）範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"name":"柏瀚國際科技有限公司（SEO 優化王）",</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"sameAs":["https://www.seoking.com.tw",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"https://www.facebook.com/seoking.tw",</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"https://www.youtube.com/@seoking",</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"https://www.linkedin.com/company/seoking"]</text>
    </g>
  </g>

  <!-- 13. Q-List 對映 -->
  <g transform="translate(50, 1870)">
    <rect width="1300" height="650" rx="20" fill="url(#qlistGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣3️⃣ Q-List 對映（問題庫 → 貼文/長文標籤）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">把「問題池」結構化映射到內容矩陣，用於 GEO 覆蓋與社群分發</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">q_list.csv 欄位：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">q_id, question, intent(4I), cluster</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">subcluster, priority, target_url</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">tag[], answer_status(draft/live)</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">owner, last_review</text>
      
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">內容標籤規格：</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">#Q:q_id、#Cluster:xxx</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">#Intent:Informational/Investigative/...</text>

      <rect x="600" y="0" width="580" height="240" rx="12" fill="rgba(254, 225, 64, 0.15)" stroke="#fee140" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">1. 生成/整併 50–200 條核心問句</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">依 4I+語音意圖分群</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">2. 每題對應一個目標落地頁</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（或新建），同步在社群排程</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">3. 每週至少 10 題轉為</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">「短貼文 + 長文錨點」的雙格式上線</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 360)">
      <rect x="0" y="0" width="580" height="140" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• q_id 不重複</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 每題都有 owner</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 每 90 天複審 last_review</text>

      <rect x="600" y="0" width="580" height="140" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">GEO 覆蓋率（命中/總題）</text>
      <text x="630" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">每題到頁面的 CTR、停留</text>
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">助理引用次數（ACR）</text>
    </g>

    <!-- 4I 意圖分類 -->
    <g transform="translate(40, 520)">
      <rect x="0" y="0" width="1220" height="100" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🎯 4I 意圖分類架構</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• Informational（資訊型）：什麼是、如何、為什麼</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• Investigative（調查型）：比較、評估、分析</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• Instructional（指導型）：步驟、教學、操作</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• Intentional（意圖型）：購買、聯繫、預約</text>
    </g>
  </g> 
 <!-- 14. FAQ 連發 -->
  <g transform="translate(50, 2550)">
    <rect width="1300" height="650" rx="20" fill="url(#faqGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣4️⃣ FAQ 連發（每主題 8–12 題，30字內直球）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">用「極短、可朗讀、可抽取」的 FAQ 提升 AIO/VSO 命中</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">faq_batch.md/csv 欄位：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">topic, q, a_short(≤30字)</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">a_long(≤120字), source_url, q_id</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">FAQPage JSON-LD（多題）</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">格式要求：</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• a_short ≤ 30字直球答案</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 20-30 秒可朗讀</text>

      <rect x="600" y="0" width="580" height="220" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 從 Q-List 抽 8–12 題高意圖問句</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">撰寫 a_short（≤30字直球）</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. 同頁加入 a_long 版本</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">+ 來源連結（提升可引性）</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">3. 以模組化區塊插入到</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Pillar/Cluster 頁；同步社群輪播</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 340)">
      <rect x="0" y="0" width="580" height="140" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• a_short 禁止贅詞與品牌硬廣</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 每題唯一答案</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 語句 20–30 秒可朗讀</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 多語系一致</text>

      <rect x="600" y="0" width="580" height="140" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">精選摘要/FAQ Rich Result 命中數</text>
      <text x="630" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">語音問句命中率（VSO_HIT）</text>
    </g>

    <!-- FAQPage JSON-LD 範例 -->
    <g transform="translate(40, 500)">
      <rect x="0" y="0" width="1220" height="120" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 FAQPage JSON-LD（短答版）範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"@type":"Question",</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"name":"AI 搜尋優化與傳統 SEO 差在哪裡？",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"acceptedAnswer":{"@type":"Answer",</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"text":"AI 時代重在可抽取與可引用：</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">問答結構、證據密度與同一實體標記是關鍵。"}</text>
    </g>
  </g>

  <!-- 15. GBP Q&A 同步 -->
  <g transform="translate(50, 3230)">
    <rect width="1300" height="550" rx="20" fill="url(#evidenceGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="520" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣5️⃣ GBP Q&amp;A 同步（2–3 條短問短答）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">把主題 Q&amp;A 下沉到本地場景，讓語音/地圖查詢可直接命中</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="200" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">每週固定 2–3 條 Q&amp;A：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">question(≤18字)</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">answer(≤40字)</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">local_modifier（地區/服務時段）</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">與官網 FAQ 同題同義不同字</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">避免重複內容疲勞</text>

      <rect x="600" y="0" width="580" height="200" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 從本週 FAQ 批次選題</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">加上地區/時段修飾詞</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 上架 GBP Q&amp;A</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">同步官網 FAQPage「可朗讀」標記</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 圖片（門面/團隊/設備）</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">每週至少 1 張更新</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 320)">
      <rect x="0" y="0" width="580" height="140" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• NAP 一致</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 分類選擇正確</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 禁止過度銷售詞</text>

      <rect x="600" y="0" width="580" height="140" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">GBP Q&amp;A 展示/互動數</text>
      <text x="630" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">來電/導覽/網站點擊 的增幅</text>
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">對比 Q&amp;A 上線週</text>
    </g>
  </g> 
 <!-- 16. Evidence Thread 四連貼 -->
  <g transform="translate(50, 3810)">
    <rect width="1300" height="650" rx="20" fill="url(#authorGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣6️⃣ Evidence Thread 四連貼（主張→數據→圖表→來源）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">把「可引用證據」打包成社群串，提升 CS_SHARE 與可引性（LLM Quotability）</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="240" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">claims.csv 欄位：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">claim_id, claim_text(≤40字)</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">metric, value, method, timeframe</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">source_url, chart_asset, alt_text</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">貼文結構：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">#1 主張、#2 數據</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">#3 圖表、#4 來源（含短鏈）</text>

      <rect x="600" y="0" width="580" height="240" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">1. 由白皮書/案例抽 3–5 條</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">可驗證主張 → 配對量化數據</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">2. 產圖（可復用圖卡模板）</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">撰寫一句可引說明與 ALT</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">3. 以 4 連貼形式在</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">X/FB/LI 發佈</text>
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">claim_id 對應頁內錨點</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 360)">
      <rect x="0" y="0" width="580" height="140" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 來源至少 2 個（原始/二手）</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 數字可追溯</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 圖表標註量測方法</text>

      <rect x="600" y="0" width="580" height="140" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">二次轉貼率、被引用段落數</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（CS_SHARE）</text>
      <text x="630" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">對應頁面停留與助理引用提升</text>
      <text x="630" y="115" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（ACR/LAR）</text>
    </g>

    <!-- 四連貼範例 -->
    <g transform="translate(40, 520)">
      <rect x="0" y="0" width="1220" height="80" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📱 四連貼範例結構</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">🧵 1/4 主張：「AISO 360™ 讓 AI 引用率提升 45%」</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">📊 2/4 數據：基於 50+ 客戶案例，3個月追蹤期</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">📈 3/4 圖表：[視覺化對比圖]</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">🔗 4/4 來源：完整研究報告 → s.seoking.tw/aiso-study</text>
    </g>
  </g>

  <!-- 17. 可視化證據 -->
  <g transform="translate(50, 4490)">
    <rect width="1300" height="650" rx="20" fill="url(#entityGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣7️⃣ 可視化證據（圖卡＋一句可引說明＋自然語言 ALT）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">讓模型與讀者「看得到、讀得懂、拿得到」證據</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="260" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">圖卡命名：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">{topic}_{claim_id}_{yyyymm}.png</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">圖卡文案：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">標題 ≤ 12 字；副標 ≤ 24 字</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">資料來源置底</text>
      
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">ALT 模版：</text>
      <text x="30" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">[圖表類型] 顯示 [指標] 在</text>
      <text x="30" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">[期間/樣本] 的 [趨勢/差異]</text>
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">關鍵數值：[x]；資料來源：[來源]</text>

      <rect x="600" y="0" width="580" height="260" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 設計統一圖卡版型</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Figma/Canva 模版 + 批次匯出</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 發佈頁插入圖卡</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">+ ImageObject JSON-LD</text>
      <text x="630" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">社群同步簡化版</text>
      
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. 以 claim_id 映射到</text>
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">長文錨點</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 380)">
      <rect x="0" y="0" width="580" height="120" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• ALT 句子通順、無專有名詞堆疊</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 圖片壓縮不失真（LCP 友善）</text>

      <rect x="600" y="0" width="580" height="120" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">圖卡被引用次數、圖片搜尋曝光</text>
      <text x="630" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">附帶 ALT 的頁面 ACR 貢獻度</text>
    </g>

    <!-- ImageObject JSON-LD 範例 -->
    <g transform="translate(40, 520)">
      <rect x="0" y="0" width="1220" height="100" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 ImageObject JSON-LD 範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"contentUrl":"https://www.seoking.com.tw/assets/cards/aiso_claim_012_202509.png",</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"caption":"2024–2025 台灣 B2B AIO 指標提升 37%，來源：SEOKING 內部儀表板",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">"creator":{"@id":"https://www.seoking.com.tw/#org"}</text>
    </g>
  </g>

  <!-- 18. Canonical 深連結 -->
  <g transform="translate(50, 5170)">
    <rect width="1300" height="550" rx="20" fill="url(#qlistGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="520" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣8️⃣ Canonical 深連結（頁內 #claim-xx 錨點）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">讓外部/AI 直接指向「可引用段落」，提升抽取效率與可監測性</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="200" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">錨點規則：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">id="claim-001"（每頁遞增）</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">對應 data-claim-id 與 claim_id</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">目錄（TOC）自動生成</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">顯示錨點</text>
      
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">對外分享用深連結（#claim-xxx）</text>

      <rect x="600" y="0" width="580" height="200" rx="12" fill="rgba(254, 225, 64, 0.15)" stroke="#fee140" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">1. Markdown/React 組件</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">為 H2/H3 自動注入穩定錨點</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">2. 於 Evidence Thread</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">以深連結回指</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">3. GA4 自訂事件 deep_link_open</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（當 URL 含 #claim-）</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 320)">
      <rect x="0" y="0" width="580" height="140" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 錨點唯一</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 改版不變動 claim_id</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 404/重導不影響錨點</text>

      <rect x="600" y="0" width="580" height="140" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">深連結點擊率</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">深連結入口的平均停留/跳出</text>
      <text x="630" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">以深連結被 AI/社群引用的次數</text>
    </g>
  </g> 
 <!-- 19. IndexNow/WebSub -->
  <g transform="translate(50, 5750)">
    <rect width="1300" height="650" rx="20" fill="url(#faqGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="620" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">1️⃣9️⃣ IndexNow／WebSub（縮短可見延遲）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">把「新/改」內容即時推送給搜尋/聚合端，降低 Time-to-Crawl/Index</text>

    <!-- 交付物規格與操作SOP -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="220" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 交付物規格</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">indexnow_key.txt</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">與自動推送腳本</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">（n8n/GitHub Actions）</text>
      
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">RSS/Atom Feed</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">+ WebSub Hub 通知推送</text>

      <rect x="600" y="0" width="580" height="220" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">1. 取得 IndexNow 金鑰</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">部署於根目錄</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">2. 內容發佈/更新後</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">批次 POST 至 IndexNow（單條或清單）</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">3. Feed 變更即觸發</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">WebSub Hub 通知</text>
      <text x="630" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">失敗重試（指數回退）</text>
    </g>

    <!-- QA檢核與KPI -->
    <g transform="translate(40, 340)">
      <rect x="0" y="0" width="580" height="120" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">✅ QA檢核</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 金鑰可讀</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 200/202 回應率 ≥ 99%</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• URL 無 301 連續跳轉</text>

      <rect x="600" y="0" width="580" height="120" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">首次收錄時間（TTCI）中位數</text>
      <text x="630" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">更新 → 快照刷新時間縮短率</text>
    </g>

    <!-- IndexNow Request 範例 -->
    <g transform="translate(40, 480)">
      <rect x="0" y="0" width="1220" height="140" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">💻 IndexNow Request 範例</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">{</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "host": "www.seoking.com.tw",</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "key": "YOUR_INDEXNOW_KEY",</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "keyLocation": "https://www.seoking.com.tw/YOUR_INDEXNOW_KEY.txt",</text>
      
      <text x="650" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  "urlList": [</text>
      <text x="650" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://www.seoking.com.tw/aisoseo/faq#claim-012",</text>
      <text x="650" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">    "https://www.seoking.com.tw/cases/aiso-360"</text>
      <text x="650" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">  ]</text>
      <text x="650" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', monospace" 
            font-size="14" fill="#666">}</text>
    </g>
  </g>

  <!-- 20. UTM for AI 來源分流 -->
  <g transform="translate(50, 6430)">
    <rect width="1300" height="750" rx="20" fill="url(#evidenceGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="720" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">2️⃣0️⃣ UTM for AI 來源分流（chatgpt/perplexity/gemini/claude）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">把「AI 助理帶來的流量/轉換」拆分到引擎/模型層級進行投資回收評估</text>

    <!-- UTM 命名準則 -->
    <g transform="translate(40, 100)">
      <rect x="0" y="0" width="580" height="280" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📋 UTM 命名準則</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">utm_source：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">chatgpt|perplexity|gemini|claude</text>
      
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">utm_medium：</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">assistant（或 ai-referral）</text>
      
      <text x="30" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">utm_campaign：</text>
      <text x="30" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">{topic|pillar}</text>
      
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">utm_content：</text>
      <text x="30" y="220" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">{claim_id|q_id}</text>
      
      <text x="30" y="250" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">自訂參數：ai_engine、ai_model、ai_surface</text>

      <!-- 操作SOP -->
      <rect x="600" y="0" width="580" height="280" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ 操作SOP</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">1. 在社群/FAQ/聲明頁</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">對外連結一律帶上述 UTM</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">2. 以短鏈服務（或自架）</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">對應 ai_session，避免 referrer 遺失</text>
      
      <text x="630" y="150" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">3. GTM 以查詢參數解析</text>
      <text x="630" y="170" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">設定 GA4 自訂維度</text>
      
      <text x="630" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">4. BigQuery 建立歸因視圖</text>
      <text x="630" y="220" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">ai_engine → 目標/交易/詢問</text>
    </g>

    <!-- 短鏈池與GA4設定 -->
    <g transform="translate(40, 400)">
      <rect x="0" y="0" width="580" height="160" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="20" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🔗 短鏈池設計</text>
      
      <text x="30" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">為每個 AI 引擎建立專屬短鏈前綴：</text>
      <text x="30" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">s.seoking.tw/cgpt/...</text>
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">s.seoking.tw/ppx/...</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">s.seoking.tw/gmn/...</text>
      <text x="30" y="130" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">s.seoking.tw/cld/...</text>

      <rect x="600" y="0" width="580" height="160" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="620" y="25" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 GA4 維度設定</text>
      
      <text x="630" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">自訂維度：</text>
      <text x="630" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">ai_engine/ai_model/ai_surface</text>
      
      <text x="630" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">自訂事件：</text>
      <text x="630" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">ai_click</text>
    </g>

    <!-- KPI儀表板 -->
    <g transform="translate(40, 580)">
      <rect x="0" y="0" width="1220" height="140" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="610" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 KPI/儀表板</text>
      
      <text x="30" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">AI 來源流量/新客占比/轉換率</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">ACR 與 LAR 對照 ai_engine 的貢獻度</text>
      <text x="30" y="105" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">單引擎 CPL/CPO/ROAS</text>
      
      <text x="650" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">所有可控外連均帶 UTM；短鏈 301 單跳</text>
      <text x="650" y="80" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">GA4 維度命中率 ≥ 95%</text>
      <text x="650" y="105" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">與後端成交/線索相互校驗</text>
    </g>
  </g>

  <!-- 統一自動化藍圖 -->
  <g transform="translate(50, 7210)">
    <rect width="1300" height="450" rx="20" fill="url(#authorGradient)" filter="url(#shadow)"/>
    <rect x="15" y="15" width="1270" height="420" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <text x="650" y="45" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="28" font-weight="bold" fill="#2d3748">🤖 統一自動化藍圖（建議）</text>
    <text x="650" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="#4a5568">整合 AISO 360™ 工具鏈，實現戰術自動化執行</text>

    <!-- 自動化工具整合 -->
    <g transform="translate(40, 100)">
      <!-- n8n -->
      <rect x="0" y="0" width="290" height="120" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
      <text x="145" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">🔄 n8n</text>
      <text x="20" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">內容上線 → 觸發 IndexNow + WebSub</text>
      <text x="20" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">同步更新 q_list.csv 狀態</text>
      <text x="20" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">推送短鏈</text>

      <!-- GitHub Actions -->
      <rect x="310" y="0" width="290" height="120" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
      <text x="455" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">⚙️ GitHub Actions</text>
      <text x="330" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">PR 合併即重建 JSON-LD</text>
      <text x="330" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">錨點檢查、死鏈掃描</text>
      <text x="330" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">圖卡 ALT 檢查</text>

      <!-- GTM/GA4 -->
      <rect x="620" y="0" width="290" height="120" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
      <text x="765" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📊 GTM/GA4</text>
      <text x="640" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">解析 UTM → 設置 ai_* 維度</text>
      <text x="640" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">deep_link_open/ai_click 事件</text>

      <!-- Looker Studio -->
      <rect x="930" y="0" width="290" height="120" rx="12" fill="rgba(196, 113, 245, 0.15)" stroke="#c471f5" stroke-width="2"/>
      <text x="1075" y="25" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="18" font-weight="bold" fill="#2d3748">📈 Looker Studio</text>
      <text x="950" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">ACR/LAR × AI 來源分流</text>
      <text x="950" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">× GEO/VSO 命中率</text>
      <text x="950" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">交叉儀表板</text>
    </g>

    <!-- 實施建議 -->
    <g transform="translate(40, 240)">
      <rect x="0" y="0" width="1220" height="160" rx="12" fill="rgba(255, 236, 210, 0.8)" stroke="#ffecd2" stroke-width="2"/>
      <text x="610" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">💡 實施建議與成功關鍵</text>
      
      <text x="30" y="60" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 優先實施戰術 11-15（基礎建設），再逐步加入 16-20（進階追蹤）</text>
      <text x="30" y="80" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 每個戰術都要設定明確的 DRI（Directly Responsible Individual）</text>
      <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 建議以 2 週為一個衝刺週期，每次實施 2-3 個戰術</text>
      <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 所有 KPI 數據需要建立基準線（Baseline），才能衡量改善效果</text>
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#f39c12">關鍵成功因素：持續監測 + 快速迭代 + 數據驅動決策</text>
    </g>
  </g>

  <!-- 頁尾公司資訊 -->
  <g transform="translate(0, 7690)">
    <rect width="1400" height="160" fill="url(#headerGradient)" filter="url(#shadow)"/>
    <rect x="20" y="20" width="1360" height="120" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 公司標誌 -->
    <circle cx="120" cy="80" r="40" fill="#667eea"/>
    <text x="120" y="70" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="20" font-weight="bold" fill="white">SEO</text>
    <text x="120" y="90" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="white">優化王</text>
    
    <!-- 公司資訊 -->
    <text x="180" y="50" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" font-weight="bold" fill="#2d3748">柏瀚國際科技有限公司 SEOKING INTERNATIONAL TECHNOLOGY CO.</text>
    <text x="180" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">創辦人：Roger Lin（林成基） | 台灣頂尖 AI 搜尋優化 AISO 360™</text>
    <text x="180" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">統一編號：27305928 | 地址：104 台北市中山區新生北路二段31之1號9樓之7</text>
    <text x="180" y="115" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">📞 02-2563-4727 | 📧 <EMAIL> | 🌐 https://seoking.com.tw</text>
    
    <!-- 版權資訊 -->
    <text x="1200" y="80" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#667eea">© 2025 AISO 360™</text>
    <text x="1200" y="100" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#667eea">All Rights Reserved</text>
  </g>

  <!-- 浮水印 -->
  <text x="1350" y="8480" text-anchor="end" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
        font-size="12" fill="rgba(102, 126, 234, 0.3)">Generated by AISO 360™ Framework</text>
</svg>