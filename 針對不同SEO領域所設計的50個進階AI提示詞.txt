作為一位經驗豐富的SEO策略師，我將深入分析您提供的資料，並圍繞「讓AI產出具備深度、策略性與實戰價值的內容」這一核心思想，為您設計一套涵蓋不同分析層面的至少50個進階AI提示詞。這些提示詞旨在挖掘文件中的深層邏輯，並將其轉化為可執行的AI搜尋優化（AI SEO）策略。
在設計這些提示詞時，我會遵循以下原則，這些原則也直接源於您的資料：
* 賦予ChatGPT明確的角色或職稱（Persona）：這有助於AI以正確的語氣和複雜度來組織回答。
* 確保提示詞清晰、簡潔、具體：越精確的提示詞，AI的回覆就越準確。
* 包含目標（Goal）：讓ChatGPT了解查詢的目的，它就能更好地調整其回應。
* 指定格式要求：明確要求輸出格式、語氣、長度、關鍵字和受眾。
* 提供額外文件或範例：餵入過去的工作、品牌指南、競爭對手頁面或參考範例，能大幅提升AI輸出的質量。
以下是針對不同SEO領域所設計的進階AI提示詞，每個提示詞都旨在提供超越表面資訊的深度分析和可執行策略：
________________


核心思想：從文件洞察中提取深度、策略性與實戰價值的 AI SEO 提示詞
本提示詞集旨在引導 AI 不僅僅是生成內容，而是成為一個能進行策略規劃、深度分析、問題診斷並提出具體可執行解決方案的 SEO 助手。我們將強調數據驅動、競爭分析、用戶意圖理解以及**投資報酬率（ROI）**的考量，讓 AI 的產出真正具備戰略價值。
________________


一、內容策略與優化 (Content Strategy & Optimization)
1. 深度競品內容差異化分析與 E-E-A-T 強化策略
   * 角色: 頂尖SEO內容策略師與E-E-A-T權威專家
   * 目標: 識別我們品牌與競爭對手在特定主題下的E-E-A-T內容差距，並提出具體優化建議以建立更強的權威性與可信度，最終提升搜尋排名。
   * 輸入: 我們關於[特定主題]的內容（請提供內容），以及至少三位主要競爭對手在此主題下的高排名內容（請提供URL或內容）。
   * 輸出: 一份詳細的表格，分析各競爭對手內容的E-E-A-T信號（包含作者經驗、專業性、權威性、內容信任度評估），並針對我們的內容，提出至少五個具體、可執行的改進點，包括如何增強專業知識、經驗、權威性與信任度，並預估每個改進點的潛在影響（ROI）和所需投入程度。
2. 目標受眾痛點映射與長尾內容主題生成
   * 角色: 洞察力敏銳的內容行銷專家
   * 目標: 針對[特定目標受眾，例如：新媽媽尋找嬰兒用品推薦]，分析其在[特定產品或服務]領域的潛在痛點與需求，並根據這些痛點，設計10個以上能有效解決問題、兼顧資訊性與商業意圖的長尾內容主題，以吸引高轉化潛力用戶。
   * 輸入: 目標受眾的詳細描述、產品/服務類別、以及我們已有的內容主題列表（請提供列表）。
   * 輸出: 一份包含10個以上內容主題的列表，每個主題都應明確指出所解決的痛點、預期搜尋意圖（資訊型、商業型、交易型、導航型），以及主要目標關鍵字與潛在的長尾關鍵字。
3. 零點擊搜尋結果頁 (Zero-Click SERP) 生存與價值最大化策略
   * 角色: 創新SEO策略顧問，專精於從零點擊SERP中創造價值
   * 目標: 針對[目標關鍵字]，該關鍵字常導致零點擊SERP（例如，特色摘要、知識面板），設計一套內容優化策略，使頁面即使不獲得點擊也能產生品牌曝光、用戶黏性或引導至其他頁面等價值。
   * 輸入: 目標關鍵字、其常見的SERP特徵（例如：People Also Ask、Featured Snippet），以及我們的頁面內容或主要資訊點。
   * 輸出: 一份詳細的策略報告，包含：如何優化內容以提高特色摘要或知識面板的機會、如何在不依賴點擊的情況下提升品牌知名度、設計哪些頁面元素（例如：微互動、摘要式CTA）以鼓勵進一步的品牌互動，並提供三種不同的內容呈現方式建議。
4. SGE (Search Generative Experience) 預測與內容架構優化
   * 角色: Google搜尋趨勢與生成式體驗分析師
   * 目標: 預測[特定查詢]在SGE環境下可能的AI生成摘要，並基於此預測，提供優化我們頁面內容結構和資訊呈現方式的建議，以提高在SGE結果中被採納或引用的機率。
   * 輸入: 目標查詢、預期受眾、目前排名靠前的頁面內容（請提供URL或內容），以及我們自身的相關內容（請提供URL或內容）。
   * 輸出: 一份SGE預測摘要範例，以及至少五項具體內容優化建議，包括如何組織資訊、使用哪些數據或實例來支持論點，以及如何建立清晰的實體關聯。
5. 內容老化 (Content Decay) 分析與策略性更新計畫
   * 角色: 內容性能與生命週期管理專家
   * 目標: 分析給定頁面的GSC數據（請提供GSC數據摘要或URL）以識別內容老化的跡象（例如：點擊率下降但排名不變），並提出一份包含至少五個高潛力內容更新建議的計畫，旨在恢復或提升頁面表現。
   * 輸入: 頁面URL、過去12個月的GSC數據（點擊、曝光、排名、CTR）、頁面目標關鍵字、當前內容。
   * 輸出: 一份分析報告，指出內容老化的潛在原因，並提供：具體的內容更新建議（例如：加入新統計數據、擴展特定章節、重寫引言）、目標關鍵字優化機會，以及預期的影響。
6. 語義關鍵字與實體整合策略 (Semantic Keyword & Entity Integration Strategy)
   * 角色: 語義SEO與知識圖譜專家
   * 目標: 針對[目標關鍵字]和[內容類型]，識別15-20個高度相關的語義關鍵字和命名實體，並提供具體的指南，說明如何自然且有策略地將這些元素整合到現有內容或新內容中，以增強Google對內容主題深度和廣度的理解。
   * 輸入: 目標關鍵字、內容類型、目標受眾簡述、當前內容草稿（請提供草稿）。
   * 輸出: 一份包含語義關鍵字和實體列表的報告，並針對內容的不同章節（例如：引言、H2部分、結論），提供至少五個具體整合範例和策略性建議，強調如何自然地豐富內容，而非堆砌。
7. 整合 SEO 與 CRO 的頁面優化建議 (Integrated SEO & CRO Page Optimization Suggestions)
   * 角色: SEO 與 CRO 整合策略師，專注於轉化率提升
   * 目標: 針對[目標頁面URL]和[主要關鍵字]，提供一份同時包含SEO和CRO優化建議的報告，旨在提升頁面性能，達到[具體目標，例如：提高電子報訂閱率、增加有機營收]。
   * 輸入: 目標頁面URL、主要關鍵字、頁面內容（請提供內容）、優化目標、以及任何已知問題（例如：跳出率高、CTA互動低）。 輸出: 一份雙欄表格報告，左欄為至少五個SEO改進建議（標題、關鍵字使用、內部連結策略），右欄為至少五個CRO改進建議（CTA位置和文案、版面建議、可讀性優化），並說明這些建議如何協同作用以達成目標。
8. 客製化內容大綱生成與競品內容超越策略
   * 角色: 內容行銷與競爭分析專家
   * 目標: 根據[核心關鍵字]、[目標受眾]和[期望字數]，生成一份詳盡的部落格文章大綱。此大綱需融入[至少兩個主要競爭對手]內容的優點，並提出獨特的改進點，確保內容在深度、廣度及獨特性上超越現有市場內容。
   * 輸入: 核心關鍵字、目標受眾描述、內容目標（例如：教育、轉化）、期望字數範圍、至少兩個主要競爭對手的URL。
   * 輸出: 一份包含H1-H3標題的完整部落格文章大綱，每個區塊都應註明其目的和預計包含的關鍵資訊。同時，提供一個「競品超越」部分，詳細說明如何結合競品優勢並加入創新元素，以打造更優質的內容。
9. 基於搜尋意圖的特色摘要內容重寫
   * 角色: 特色摘要優化專家
   * 目標: 根據[目標關鍵字]的搜尋意圖，將[現有內容]重寫為更可能被選為特色摘要的簡潔、易掃描格式，特別是針對[目標競爭對手]目前採用的[內容格式]進行改進。
   * 輸入: 目標關鍵字、現有頁面內容、目標競爭對手目前排名特色摘要的內容及其格式。
   * 輸出: 重寫後的內容片段，以bulleted list或短段落形式呈現，並解釋為何此格式和內容能更有效提高獲取特色摘要的機會。
10. 多頁面標題標籤與元描述的 A/B 測試假說生成
   * 角色: 數據驅動型SEO經理
   * 目標: 針對[給定URL列表]，生成每頁五組不同的標題標籤（<70字）和元描述（140-160字），旨在最大化點擊率（CTR），並為每組提出一個具體的A/B測試假說。
   * 輸入: URL列表、每頁的主要目標關鍵字、品牌CTA（如有）。
   * 輸出: 一份表格，列出每個URL的五組標題標籤和元描述，以及對應的A/B測試假說，假說應包含預期結果、衡量指標及測試原因，並註明需要進行字數檢查。
11. 根據內容權重與相關性進行內部連結策略規劃
   * 角色: 網站架構與內部連結專家
   * 目標: 審查[核心頁面內容]和[網站Sitemap或相關URL列表]，設計一套提升頁面權重和搜尋引擎理解的內部連結策略，不僅建議連結來源和目標，更要考慮**連結權重流動（link equity flow）與主題叢集（topic clusters）**的建立。
   * 輸入: 核心頁面內容、網站Sitemap或相關URL列表、指定要強化權重的目標頁面（如有）。
   * 輸出: 一份內部連結建議列表，以表格形式呈現，包含：來源頁面、目標頁面、建議錨文本、以及此連結的策略目的（例如：強化[特定主題]的權威性、將權重從[高權重頁面]傳遞到[新頁面]）。
12. AI生成內容的品質控制與 E-E-A-T 合規性審查框架
   * 角色: AI內容審核專家與道德規範顧問
   * 目標: 考慮到Google對AI內容的指導原則（專注於質量而非生產方式），設計一套審查AI生成SEO內容的框架，以確保其符合E-E-A-T標準，並避免被視為操縱排名的垃圾內容。
   * 輸入: Google對AI內容的指導原則（請提供指南摘要）、我們的品牌語氣和內容質量標準。
   * 輸出: 一份AI生成內容審核框架，包含：評估內容原創性與深度、事實準確性、語氣與品牌一致性、是否提供獨特價值、以及E-E-A-T信號的檢查清單。同時，提供一個「人類編輯介入點」的建議流程圖。
二、關鍵字研究與受眾分析 (Keyword Research & Audience Analysis)
13. 新產品/服務的利基市場與長尾關鍵字深度挖掘
   * 角色: 市場情報分析師與利基市場SEO研究員
   * 目標: 針對[新產品/服務]在[目標產業]，發掘100個以上具有明確商業潛力、且競爭度相對較低的利基市場與長尾關鍵字，並按搜尋意圖和行銷漏斗階段進行分類，為新內容策略奠定基礎。
   * 輸入: 新產品/服務的詳細描述、目標產業、核心概念詞。
   * 輸出: 一份詳細的關鍵字列表，以表格形式呈現，包含：關鍵字、預估搜尋意圖、行銷漏斗階段、長尾與短尾分類，以及「內容機會」備註欄。強調發掘高轉化潛力的長尾詞。
14. 基於競爭對手分析的問答型關鍵字與內容策略
   * 角色: 內容空白點與用戶問題專家
   * 目標: 分析[主要競爭對手A]和[主要競爭對手B]在[目標關鍵字]領域的「People Also Ask」內容和常見問答，識別出他們尚未充分覆蓋或可優化的問答型關鍵字，並為我們生成至少10個用於FAQ區塊或獨立內容的問答主題。
   * 輸入: 目標關鍵字、競爭對手URL、我們目前的FAQ內容（若有）。
   * 輸出: 一份問答型關鍵字列表，每個問題都附有簡潔的答案草稿，並註明其適合的搜尋意圖和內容形式（FAQPage Schema、部落格文章、服務頁面）。
15. 關鍵字群集與內容主題地圖規劃 (Keyword Clustering & Content Topic Map Planning)
   * 角色: 主題權威性與內容架構師
   * 目標: 提供[關鍵字列表]，將其分組為清晰的主題叢集（Topic Clusters），並為每個叢集建議一個「支柱頁面（Pillar Page）」主題及多個「輔助內容（Supporting Content）」主題，以建立強大的主題權威性。
   * 輸入: 關鍵字列表、網站Sitemap或現有內容主題（若有）。
   * 輸出: 一份主題地圖報告，以層級式列表呈現，包含：各主題叢集的名稱、建議的支柱頁面標題、以及每個支柱頁面下的3-5個輔助內容主題，並說明此架構如何提升整體網站權威。
16. 透過 Alphabet Soup 方法發掘新興趨勢與隱藏版長尾關鍵字
   * 角色: 趨勢預測與長尾關鍵字獵人
   * 目標: 執行自動化的「Alphabet Soup Method」針對[核心關鍵字]，不僅列出A-Z的關鍵字建議，更要分析這些建議詞中潛在的新興話題、用戶需求變化或未被充分利用的長尾關鍵字機會。
   * 輸入: 核心關鍵字。
   * 輸出: 一份包含長尾關鍵字列表的報告，並在「備註」欄位標註潛在的新興趨勢或利基市場，同時提出三個可利用這些關鍵字建立獨特內容的策略方向。
17. 根據行銷漏斗階段的用戶體驗設計與 CTA 策略
   * 角色: 行銷漏斗與用戶體驗優化專家
   * 目標: 針對[給定關鍵字列表]，判斷其所屬的行銷漏斗階段，並為每個階段提供具體的內容格式、用戶體驗設計元素和Call-to-Action (CTA) 建議，以最大化轉化潛力。
   * 輸入: 關鍵字列表。
   * 輸出: 一份表格，包含關鍵字、所屬行銷漏斗階段、建議的內容格式（例如：頂層漏斗：教育性部落格；底層漏斗：產品頁面/試用登記）、相應的UX設計考量，以及至少兩種不同的CTA文案範例。
18. 電商產品類別的內容創新與銷售策略
   * 角色: 電商內容行銷大師
   * 目標: 針對[特定電商產品類別]（例如：Jeep儀表板橡膠鴨），在考量[目標受眾]和[我們的獨特賣點]（例如：更高品質、更快出貨）的情況下，生成10個以上具備創新性且能直接促進銷售的部落格內容點子。
   * 輸入: 產品類別、目標受眾描述、品牌獨特賣點、已發布的內容列表及表現最佳的內容類型、希望推銷的具體產品。
   * 輸出: 一份包含10個以上內容點子的列表，每個點子都應明確說明如何結合品牌獨特賣點、如何引導用戶至特定產品頁面，並預期達成的可衡量KPI（例如：提高[特定產品]的銷售量）。
19. 競爭對手目標受眾與關鍵字策略的逆向工程
   * 角色: 競爭情報與受眾分析師
   * 目標: 針對[競爭對手的一系列高排名頁面]，逆向工程出其主要目標受眾畫像和核心關鍵字策略，並為我們品牌的SEO策略提供至少三項可借鑒或差異化的洞察。
   * 輸入: 競爭對手高排名頁面的URL列表。
   * 輸出: 一份表格，分析每個頁面的預期受眾畫像、主要目標關鍵字、以及內容如何服務這些受眾。最終提供一份總結，提出我們應如何調整或加強自己的受眾定位和關鍵字策略。
20. 針對多語系或跨地區市場的關鍵字本土化策略
   * 角色: 全球化SEO策略師
   * 目標: 針對[核心產品/服務]和[目標地區/語言，例如：台灣繁體中文市場]，提供一份本土化的關鍵字研究策略，發掘除了直接翻譯之外，更符合當地文化語境和搜尋習慣的關鍵字變體和相關詞彙。
   * 輸入: 核心產品/服務描述、主要英文關鍵字（若有）、目標地區/語言。
   * 輸出: 一份包含至少20個本土化關鍵字的列表，按搜尋意圖分類，並提供針對這些關鍵字進行內容創意的建議，強調文化適應性。
21. 關鍵字難度與流量潛力評估模型建議
   * 角色: SEO投資報酬率分析師
   * 目標: 基於每月搜尋量和關鍵字難度（需AI模擬），為[特定行業]的[目標關鍵字列表]提出一個優化和優先級排序模型，以識別高投資報酬率（ROI）的關鍵字機會，並建議哪些關鍵字應優先投入內容製作。
   * 輸入: 目標關鍵字列表、行業描述。
   * 輸出: 一份模型框架建議，包含評估關鍵字潛力的標準（例如：搜尋量/難度比、轉化潛力），並對提供的關鍵字進行初步優先級排序，提出至少三項策略建議，說明如何利用此模型來指導內容策略。
三、技術SEO與網站架構 (Technical SEO & Site Architecture)
22. 基於爬行日誌數據的深度爬行優化策略
   * 角色: 技術SEO顧問與數據分析師，專精於爬行預算優化
   * 目標: 分析[提供的爬行日誌數據]（假設AI能讀懂），識別潛在的爬行錯誤、重複內容問題或阻礙爬行器效率的頁面，並生成一份包含至少五個具體、可執行的爬行優化建議，以確保Google更有效地索引我們的優質內容。
   * 輸入: 爬行日誌數據（請提供數據摘要或關鍵模式）。
   * 輸出: 一份詳細的分析報告，列出發現的爬行問題及其潛在影響，並提供優先級排序的優化建議（例如：Robots.txt調整、內部連結結構改進、站點速度優化），預期每個建議的實施效果。
23. 跨平台Schema Markup策略與驗證
   * 角色: Schema Markup架構師
   * 目標: 針對[網站核心頁面類型，例如：產品頁、部落格文章、關於我們頁]，設計一套整合性的Schema Markup策略，不僅建議特定Schema類型，更要考量其在不同搜尋引擎結果（Google、Bing等）和跨平台（桌面、行動）的展示效果與驗證方法。
   * 輸入: 網站核心頁面類型描述、主要目標（例如：提高FAQ特色摘要、增強本地商家曝光）、競爭對手Schema使用情況（若有）。
   * 輸出: 一份多頁面Schema Markup策略，以表格形式列出每個頁面類型建議的Schema、關鍵屬性（key properties）、以及JSON-LD代碼範例。強調如何確保跨平台的一致性與未來可擴展性，並提醒需人工驗證。
24. 複雜網站的 Robots.txt 策略與風險評估
   * 角色: 高級技術SEO專家，專精於大型網站的Robots.txt管理
   * 目標: 為[特定複雜網站類型，例如：大型電商網站、多媒體內容網站]生成一份Robots.txt文件，不僅包含基本指令，更要考量如何平衡搜尋引擎爬行效率與保護敏感/低價值頁面的需求，並對潛在的誤封鎖風險進行評估。
   * 輸入: 網站域名、特定需要阻擋或允許爬行的目錄/檔案清單（例如：/wp-admin、staging子目錄、CSS/JS檔案）、XML Sitemap連結。
   * 輸出: 一份優化後的Robots.txt文件，並提供一份「風險評估」說明，指出哪些指令可能導致意想不到的爬行問題，以及如何進行測試。
25. 動態 XML Sitemap 生成與索引優先級策略
   * 角色: 網站索引優化專家
   * 目標: 針對[包含大量動態內容或頻繁更新的大型網站]，生成一份XML Sitemap，並提出一種動態更新Sitemap的邏輯和索引優先級策略，以確保Google能快速發現並索引最重要的內容。
   * 輸入: 網站類型（例如：新聞網站、論壇）、提供一系列需索引的URL（請提供URL列表）、更新頻率需求。
   * 輸出: XML Sitemap的生成邏輯，並提供一個優先級排序策略，說明哪些頁面應被視為高優先級（例如：最近更新的產品、熱門部落格文章），以及如何通過Sitemap協議（例如：<priority>和<lastmod>標籤）來傳達這些信息。
26. 重複內容與內容蠶食（Cannibalization）的深度診斷與解決方案
   * 角色: 內容結構與索引專家
   * 目標: 審查[提供的100個頁面的元數據和H1標題]，識別潛在的重複內容風險或內容蠶食問題，並為每個問題提供至少兩種具體的解決方案，例如：Canonicalization、Noindex、內容合併或重新優化。
   * 輸入: 100個頁面的元數據和H1標題列表（請提供數據）。
   * 輸出: 一份詳細的診斷報告，列出所有被識別出的重複內容/蠶食問題，並針對每個問題提供具體的建議（包括實施方法和預期效果），以及如何避免未來再次發生。
27. 針對不同CMS和開發支持水平的客製化技術SEO審計清單
   * 角色: 實用型技術SEO經理
   * 目標: 根據[網站類型、CMS、開發支持水平和近期變更]，生成一份客製化且具備優先級排序的技術SEO審計清單，旨在檢測網站的當前健康狀況，並為經驗不足的團隊成員提供清晰的操作指南。
   * 輸入: 網站類型（小型本地、企業級B2B等）、CMS類型、開發支持水平（無、少量、充足）、近期網站變更（例如：網站遷移、新頁面添加）、任何觀察到的問題（例如：流量下降）。
   * 輸出: 一份詳盡的技術SEO審計清單，以優先級排序（高、中、低），每項檢查點都包含：檢查目的、簡要操作步驟、預期發現，並根據開發支持水平提供實施提示。
28. 核心網頁指標 (Core Web Vitals) 問題診斷與優化路徑
   * 角色: 網站性能工程師與SEO專家
   * 目標: 針對[網站URL]的Core Web Vitals報告（假設AI能讀懂），診斷主要問題，並提供一份包含至少五個具體優化步驟的實戰路線圖，以改善LCP、FID/INP、CLS指標，提升用戶體驗和搜尋排名。
   * 輸入: 網站URL、簡要的Core Web Vitals報告摘要（例如：LCP不佳、CLS問題）。
   * 輸出: 一份Core Web Vitals優化路線圖，列出主要問題、技術根源分析、以及分階段的解決方案（例如：優化圖片、延遲加載CSS、減少JS執行時間），並預估每個步驟的改善效果。
29. Hreflang 實施策略與錯誤檢查
   * 角色: 國際SEO架構師
   * 目標: 針對[多語言或多地區網站]，設計一套Hreflang實施策略，包含不同語言/地區版本的連結結構，並建議常見的Hreflang錯誤檢查方法。
   * 輸入: 網站的主要語言和地區版本（例如：en-US, en-GB, zh-TW）、網站URL結構範例。
   * 輸出: 一份Hreflang實施指南，包含：建議的Hreflang標籤格式、放置位置（head、HTTP header、Sitemap）、以及針對常見錯誤（例如：缺少返回連結、語法錯誤）的檢查清單。
30. 結構化數據錯誤分析與修正建議
   * 角色: 結構化數據專家
   * 目標: 針對[提供的JSON-LD代碼片段或頁面內容]，檢查其是否符合Google的結構化數據指南，並識別出錯誤或不完整之處，提供具體修正建議，以提高富摘要（Rich Snippets）的機會。
   * 輸入: JSON-LD代碼片段或頁面內容、期望的Schema類型（例如：Product、Article）。
   * 輸出: 一份結構化數據審核報告，列出發現的錯誤（例如：缺少必填屬性、數據類型不匹配）、警告，並提供詳細的修正指南和優化建議，以最大限度地發揮結構化數據的潛力。
31. 圖片SEO優化策略與自動化建議
   * 角色: 多媒體內容SEO專家
   * 目標: 針對[電商網站或內容型網站]，設計一套全面的圖片SEO優化策略，包含圖片壓縮、替代文本（alt text）、檔案命名、Lazy Loading、WebP格式轉換等，並建議如何透過自動化工具或CMS功能實現這些優化。
   * 輸入: 網站類型、現有圖片使用情況（例如：大量產品圖）、目標關鍵字（若有）。
   * 輸出: 一份圖片SEO策略報告，涵蓋：圖片優化最佳實踐、自動化工具推薦、alt text撰寫規範（包含關鍵字使用）、以及如何利用圖片Sitemap提升索引。
四、本地SEO與品牌管理 (Local SEO & Brand Management)
32. Google商家檔案 (GBP) 內容差異化與在地權威建立策略
   * 角色: 品牌在地化策略師
   * 目標: 針對[本地商家類型]在[特定城市]，生成五個獨特且符合品牌調性（請提供品牌調性）的Google商家檔案描述，旨在凸顯商家特色，提升在地搜尋排名，並能透過描述建立在地權威感，同時避免關鍵字堆砌。
   * 輸入: 商家名稱、產品/服務簡述、目標客戶痛點、目標關鍵字、附近地標、品牌調性。
   * 輸出: 五個風格各異但都符合規範的GBP描述，每個描述都附有簡要的策略說明，解釋其如何吸引目標受眾、整合在地關鍵字，並提供一個強調「如何從競爭對手中脫穎而出」的建議。
33. 多地點服務頁面的規模化內容生成與優化模板
   * 角色: 多地點SEO專家
   * 目標: 為一個[連鎖商家類型]在[多個城市]生成標準化的地點頁面內容模板，該模板需包含關鍵的在地化元素、品牌特色、用戶痛點解決方案、圖片與地圖的佔位符，並能有效適應不同地點的獨特資訊。
   * 輸入: 商家類型、城市列表、目標受眾、品牌價值主張、品牌語氣、競爭對手地點頁面範例（喜歡與不喜歡的）。
   * 輸出: 一份詳細的地點頁面內容模板，包含H1-H3標題、引言邏輯、在地化資訊區塊（例如：附近地標、交通資訊、當地客戶評價）、CTA、圖片/地圖佔位符，並說明如何透過此模板實現內容的規模化生成與在地化優化。
34. 在地商家評論管理與品牌聲譽提升策略
   * 角色: 客戶體驗與在地聲譽管理師
   * 目標: 針對[本地商家]的客戶評論（正面、負面、中性），制定一套品牌化的回應策略，不僅提供回應範例，更要考量如何透過回應建立正向品牌形象、化解負面情緒，並將關鍵的客戶反饋轉化為服務改進的機會。
   * 輸入: 商家名稱、品牌語氣、過往回應範例（正面、負面、中性）、提供近期客戶評論範例。
   * 輸出: 一套包含：回應指南、正面、負面（引導至線下解決）及中性評論的至少三種回應範本，並提供「如何利用評論數據識別服務改進機會」的分析框架。
35. 在地反向連結與引用源的策略性建構計畫
   * 角色: 在地市場連結建設專家
   * 目標: 為[新地點的商家]在[特定城市]，制定一份全面的在地反向連結和引用源建構計畫，包含：識別高權威在地媒體/社群組織、基於競品分析的連結機會，並提供客製化的外展郵件模板。
   * 輸入: 商家類型、新地點城市/州、目標受眾、主要在地SEO關鍵字、主要競爭對手列表。
   * 輸出: 一份分階段的在地連結建構計畫，包含：高潛力在地媒體/部落格列表、在地目錄和社群組織列表、基於競品分析的獨特連結機會，以及兩個不同語氣（例如：專業、友善）的客製化外展郵件模板。
36. 在地活動與合作夥伴的SEO效益最大化策略
   * 角色: 在地行銷與SEO整合專家 目標: 針對[本地商家]，提供一份如何將在地活動（例如：贊助、參與市集）和合作夥伴關係的效益，最大化地轉化為在地SEO信號和品牌知名度的策略。
   * 輸入: 商家類型、所在城市、計劃參與的在地活動類型、潛在合作夥伴類型。
   * 輸出: 一份策略報告，包含：活動相關的內容創作建議（例如：活動頁面、部落格文章、社交媒體發布）、如何從合作夥伴處獲取在地反向連結、以及如何利用UGC（User Generated Content）提升在地搜尋可見度。
五、SEO報告與數據分析 (SEO Reporting & Data Analysis)
37. GSC數據的異常模式偵測與深度歸因分析
   * 角色: 進階SEO數據分析師，專精於問題診斷與歸因
   * 目標: 根據[提供的GSC數據]和[描述的網站問題]，不僅總結數據，更要識別數據中的異常模式（例如：特定關鍵字組的CTR異常下降），並進行深度歸因分析，提出至少五個最可能的根本原因和對應的解決方案。
   * 輸入: 網站名稱、詳細的GSC數據（請提供數據）、具體網站問題描述（例如：點擊下降但曝光不變）、問題持續時間、過去的解決嘗試、季節性趨勢、近期網站變更。
   * 輸出: 一份詳盡的數據分析報告，以清晰易懂的語言呈現，包含：異常模式的識別、多個歸因假設（含佐證數據）、以及至少五個具體、可執行的解決方案，並附帶對這些解決方案潛在影響的評估。
38. 客製化客戶SEO績效報告生成與策略建議
   * 角色: 資深SEO客戶經理，專精於技術術語的商業化翻譯
   * 目標: 根據[提供的SEO數據]和[客戶的業務目標]，生成一份易於理解、且能清楚展現SEO價值和未來策略方向的客戶報告摘要，將所有技術術語轉化為商業語言，並提出下一步的策略建議。
   * 輸入: 技術SEO數據（例如：GSC、GA4數據摘要）、客戶的業務目標（例如：增加品牌知名度、提高線上銷售）、報告重點（例如：上個月的關鍵成果）。
   * 輸出: 一到兩段的客戶摘要，解釋關鍵SEO指標的表現及其對業務的影響。同時，提供一份「策略展望」部分，提出至少三個基於數據的下一步行動建議，並解釋這些行動如何支持客戶的業務目標。
39. 綜合性有機流量變動的跨渠道歸因分析
   * 角色: 跨渠道行銷分析師
   * 目標: 針對[提供的網站流量數據，包括MoM和YoY有機流量變化]，不僅解釋其波動，更要結合[外部因素，例如：Google演算法更新、市場趨勢、競爭活動]進行跨渠道歸因分析，提出至少三個可驗證的假設，解釋流量變動的原因，並建議優化策略。
   * 輸入: MoM和YoY有機流量數據、頁面優化時間、任何已知外部因素。
   * 輸出: 一份綜合分析報告，詳細解釋流量變動的原因，並提出可進行A/B測試或進一步優化的具體建議（例如：重新調整內容策略、調整廣告投放），旨在提升長期流量和轉化。
40. 基於業務模式的SEO KPI框架設計與測量方法
   * 角色: 業務導向SEO策略師
   * 目標: 針對[特定業務模式，例如：B2B SaaS、電商、內容出版商]，設計一套全面的SEO KPI框架，不僅建議關鍵指標，更要說明如何將這些KPI與公司的SMART業務目標（例如：年化有機營收增長15%）對齊，並提供測量方法。
   * 輸入: 業務名稱、產品/服務、行業、主要競爭對手、SMART SEO目標。
   * 輸出: 一份SEO KPI框架，以表格形式列出：建議的KPI（例如：有機流量、轉化率、品牌搜尋量）、測量工具（例如：GSC、GA4）、與業務目標的關聯，以及追蹤和報告這些KPI的方法論。
41. 預算分配與 ROI 最大化的 SEO 策略建議
   * 角色: SEO投資報酬率顧問
   * 目標: 在[給定總SEO預算]的情況下，根據[網站現狀]和[SMART SEO目標]，提出一份最佳的SEO預算分配策略，涵蓋內容、技術、連結建設等不同領域，以實現最高的投資報酬率。
   * 輸入: 總SEO預算、網站現狀概述（例如：技術問題多、內容不足、連結權威性低）、SMART SEO目標（例如：一年內提升有機流量20%）。
   * 輸出: 一份預算分配建議報告，列出各SEO領域的建議投入比例和具體支出方向，並解釋為何此分配能最大化ROI，同時附帶風險評估和備用方案。
六、競爭情報與市場洞察 (Competitive Intelligence & Market Insights)
42. 全面的競爭對手SEO策略拆解與戰術建議
   * 角色: 競品分析專家
   * 目標: 針對[至少三位主要競爭對手]，進行全面的SEO策略拆解，分析其在內容風格、關鍵字使用、連結建設、元數據優化等方面的戰術，並為我們品牌提出至少五項可超越或差異化的戰術建議。
   * 輸入: 我們的網站URL、至少三位競爭對手的URL、我們希望提升的領域（例如：有機排名、流量、營收）。
   * 輸出: 一份詳細的競品分析報告，以表格或列表形式呈現，比較各競爭對手的優勢與劣勢，並提出具體的戰術建議，包括如何模仿成功的戰術並在某些方面做得更好，以及如何避免競爭對手的失敗經驗。
43. 競爭對手內容差距分析與內容日曆建議
   * 角色: 內容策略規劃師
   * 目標: 比較[我們網站的已發布部落格URL列表]與[至少三位主要競爭對手]的內容，識別出我們尚未涵蓋但競爭對手已排名的主題和關鍵字，並根據這些差距，提出一份包含至少10個高潛力內容主題的內容日曆草稿。
   * 輸入: 我們網站的部落格URL列表、至少三位競爭對手的URL。
   * 輸出: 一份內容差距分析報告，列出競爭對手排名但我們尚未涵蓋的主題和相關關鍵字。隨後，提供一份包含至少10個內容主題的內容日曆草稿，每個主題都註明目標關鍵字、預期搜尋意圖和初步內容方向。
44. 競爭對手 E-E-A-T 信號強化與內容改進計畫
   * 角色: 內容權威性建築師
   * 目標: 分析[競爭對手的內容]如何傳達E-E-A-T信號，並與[我們自身的E-E-A-T內容]進行對比。基於此分析，提出一份具體的內容改進計畫，包含至少五項可提高我們E-E-A-T的策略，並評估其潛在投資報酬率和實施難度。
   * 輸入: 競爭對手高排名內容（請提供URL或內容）、我們自己的相關E-E-A-T內容（請提供URL或內容）。
   * 輸出: 一份表格，詳細說明競爭對手如何展現E-E-A-T，並針對我們的內容，提出改進建議，包括：增強作者專業性、引用權威來源、增加案例研究等，並附帶ROI和實施難度評估。
45. 新興競爭者監測與潛在威脅分析
   * 角色: 市場情報與風險管理專家
   * 目標: 針對[特定行業]和[產品/服務]，識別潛在的新興競爭對手，分析其SEO策略（例如：內容方向、關鍵字選擇），並評估其對我們品牌的潛在威脅和機會。
   * 輸入: 我們的行業、產品/服務、當前已知競爭對手。
   * 輸出: 一份新興競爭對手分析報告，列出3-5個潛在的新興競爭者，分析其SEO活動的關鍵特徵，並提出我們應如何調整策略以應對這些威脅或抓住相關機會。
46. 反向連結機會與連結建設策略的競品分析
   * 角色: 連結建設策略師
   * 目標: 分析[至少兩個主要競爭對手]的反向連結來源和連結類型，識別出我們未曾開發的高質量連結機會，並基於此提出一份包含至少五個具體策略的連結建設計畫，以提升我們網站的域名權重。
   * 輸入: 我們的網站URL、至少兩個主要競爭對手的URL。
   * 輸出: 一份競品連結分析報告，列出競爭對手的主要連結來源和連結策略（例如：內容行銷、客座文章、資源頁面連結），並提出我們可以採用的具體連結建設機會，以及一個客製化的外展郵件模板。
七、進階與非傳統SEO策略 (Advanced & Unconventional SEO Strategies)
47. 程序化SEO (Programmatic SEO) 頁面模板與邏輯設計
   * 角色: 程序化SEO架構師
   * 目標: 針對[關鍵字格式，例如：「最佳[服務/產品]在[城市]」]，設計一個完整的程序化SEO頁面模板與邏輯，包含動態生成的標題、內容區塊（H1-H3）、元數據、變數（例如：城市、服務類型、評論），以及內部連結邏輯，以實現大規模的內容生成與優化。
   * 輸入: 關鍵字格式、目標受眾、期望頁面結構（H1、H2、H3、引言、結尾、CTA等）、數據變數（data variables）、寫作風格/語氣。
   * 輸出: 一份詳細的程序化SEO模板設計，以結構化文本呈現，包含：各區塊的動態內容生成規則、元數據模板、內部連結策略（例如：依據地理位置或主題關聯性），以及一個簡短的FAQ部分，並強調其可擴展性。
48. SERP特徵預測與內容呈現策略
   * 角色: 搜尋結果頁預測專家
   * 目標: 針對[目標關鍵字]和[內容類型]，預測最可能出現的SERP特徵（例如：圖片輪播、視頻、熱門問題），並據此設計一份內容呈現策略，以最大化我們內容出現在這些特徵中的機會。
   * 輸入: 目標關鍵字、內容類型、內容目標（教育、轉化）、關鍵字搜尋意圖、已知SERP特徵（若有）。
   * 輸出: 一份SERP特徵預測報告，列出預期的SERP特徵及其可能性，並提供具體的內容結構和格式建議（例如：如何優化圖片以出現在圖片輪播、如何製作視頻摘要以獲取視頻特徵），以及如何利用Schema Markup。
49. 利用AI進行搜尋意圖演變分析與前瞻性內容規劃
   * 角色: 搜尋意圖動態分析師
   * 目標: 根據[過去12-24個月的GSC數據]和[特定行業的趨勢變化]，分析[主要關鍵字]搜尋意圖的潛在演變，並提出一份前瞻性的內容規劃，以適應未來的用戶需求和搜尋行為。
   * 輸入: 主要關鍵字列表、過去12-24個月的GSC數據摘要（請提供數據）、特定行業趨勢報告（若有）。
   * 輸出: 一份搜尋意圖演變分析報告，指出關鍵字意圖的潛在變化（例如：從資訊型轉向商業型），並提出至少三項前瞻性內容策略，包括如何預先準備不同意圖的內容，以維持搜尋可見度。
50. 長尾關鍵字叢集的連結建設與內容推廣策略
   * 角色: 長尾SEO與連結建設專家
   * 目標: 針對[已識別的長尾關鍵字叢集]，設計一套整合性的連結建設與內容推廣策略，不僅吸引相關反向連結，更能提升內容在利基市場中的可見度與權威性。
   * 輸入: 長尾關鍵字叢集列表、相關內容URL（若有）、目標受眾描述。
   * 輸出: 一份策略報告，包含：針對長尾內容的獨特連結建設機會（例如：行業資源頁面、特定社群討論）、內容推廣管道建議（例如：利基論壇、合作夥伴發布）、以及如何衡量這些活動的成功。
51. AI驅動的內容審核與刷新頻率建議
   * 角色: 內容生命週期管理專家
   * 目標: 基於[網站已發布內容的列表]和[歷史性能數據]，利用AI識別哪些內容應被審核、更新或移除，並為不同類型內容推薦最佳的刷新頻率，以維持內容的新鮮度和排名。
   * 輸入: 網站內容列表（URL、發布日期、內容類型）、過去12個月的GSC/GA4性能數據（請提供數據）。
   * 輸出: 一份內容審核與刷新建議報告，以表格形式呈現，包含：內容URL、審核建議（更新、擴展、合併、移除）、建議的刷新頻率，並說明判斷依據（例如：流量下降、關鍵字排名流失、內容過時）。
52. 基於使用者行為數據的內容個人化建議
   * 角色: 個性化內容策略師
   * 目標: 假設AI可以分析[網站的使用者行為數據，例如：停留時間、點擊路徑、回訪頻率]，提出針對不同用戶群體進行內容個人化的策略建議，以提升用戶體驗和轉化率。
   * 輸入: 用戶行為數據的抽象描述、網站內容類型、目標用戶群體定義。
   * 輸出: 一份內容個人化策略建議報告，包含：如何識別不同的用戶群體（例如：新訪客、回訪者、特定產品興趣者）、針對這些群體推薦的內容調整方向（例如：首頁內容、推薦文章、CTA）、以及預期的SEO/CRO效益。
________________


這些提示詞的設計，旨在將您提供的資料中的最佳實踐，結合對SEO「深層邏輯」的理解，引導AI從單純的內容生成者轉變為一個能提供策略性指導和可執行洞察的SEO合作夥伴。請根據您的具體情況填入相應的詳細資訊，以獲得最精準的AI輸出。