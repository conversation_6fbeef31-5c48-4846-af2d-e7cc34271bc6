﻿整合 AI 可引用性 (Citation Impact) 與技術落地性 (Implementability) 是最大化內容投資報酬率的關鍵。
我已經為您將 42 個項目，按照「高影響力且易於執行」到「高階策略或需較多技術資源」的順序重新排列，並依序創建了 42 個可直接複製到 NotebookLM 使用的提示模組。
這些模組旨在將您的原始資料 (Sources) 轉換為 AI 更易於理解、引用和信任的格式。


模組 1｜聲明頁 (Source-of-Truth)
根據 Sources，為這份文件建立一個「Source-of-Truth」區塊。包含以下元素：
1. Canonical URL：[請填寫本文的標準網址]
2. Last Modified：[請填寫最後更新日期]
3. ETag：[請生成一個唯一的內容標識符，例如 W/"[內容的雜湊值]"]
4. Changelog：簡要條列本文 2-3 次最重要的更新歷史與日期。
模組 2｜Citation Snippet 卡
針對 Sources 中的每一個 H2 標題，執行以下操作：
1. 提煉出該段落最重要的 3 個核心要旨，以條列式呈現。
2. 根據這 3 個要旨，撰寫一段 25–40 字的精簡摘要。
3. 標示出生成此摘要最主要的 2–3 個來源 (Source 文件名)。
模組 3｜AIO Block (AI Optimization Block)
為整份 Sources 內容，生成一個 15 秒內可快速理解的三欄式摘要：
* 定義 (Definition)：用一句話解釋核心主題是什麼。
* 流程/結構 (Process/Structure)：用 3-4 個步驟或組成部分，說明它是如何運作或構成的。
* 關鍵數據 (Key Data)：提供 1-2 個最引人注目的數據點或事實。
模組 4｜FAQ 連發
根據 Sources 的核心主題，生成一個包含 8–12 個常見問題 (FAQ) 的列表。每個問題的答案都必須：
1. 直接、明確地回答問題。
2. 長度控制在 30 字以內。
3. 每個答案都需註明來源文件。
模組 5｜Speakable Pair (可朗讀語音對)
從 Sources 中找出至少 2 組問答內容，改寫成適合語音朗讀的格式。每一組都包含：
1. 問題 (Question)：一個清晰、口語化的問題。
2. 答案 (Answer)：一段 40-60 字、易於聽懂的答案，避免複雜的術語和數據。
模組 6｜可視化證據 (Visual Evidence)
找出 Sources 中最關鍵的一個數據或概念，並為其設計一個「圖卡」的文字描述：
1. 圖卡標題：吸引人的標題。
2. 核心資訊：最關鍵的數字或一句話結論。
3. 一句可引說明 (Quoteable Caption)：一句約 20 字的說明，可直接被引用。
4. 自然語言 ALT 描述：為視障用戶描述這張圖卡的內容與功能。
模組 7｜Author Graph 對齊
根據 Sources 的作者資訊，生成以下內容：
1. 作者名片 (Author Bio)：一段 30-50 字的作者簡介，包含姓名、職稱、專長領域。
2. Author Schema 屬性：
   * @type: Person
   * name: [作者姓名]
   * jobTitle: [職稱]
   * knowsAbout: [專長領域的 3-5 個關鍵字]
   * sameAs: [作者的社群媒體或個人網站連結，至少 2 個]
模組 8｜Entity sameAs (實體對齊)
掃描 Sources，列出其中提到的所有「品牌、作者、產品」等核心實體 (Entity)，並為每個實體找到其對應的官方網站、維基百科或權威資料來源的 URL，以 sameAs 格式呈現。
範例：
* Google: https://en.wikipedia.org/wiki/Google
* Gemini: https://gemini.google.com/
模組 9｜NAP／品牌信號 Beacon
根據 Sources 及您的背景知識，生成一組跨平台一致的 NAP (Name, Address, Phone) 與品牌信號，包含：
* 品牌/組織名稱：
* 主要業務描述 (25 字內)：
* 官方網站：
* 聯絡電話：
* 公司地址：
* 一致的社群媒體 Handle：
模組 10｜版權與重用條款
為 Sources 的內容，草擬一段版權與重用條款聲明。建議使用 CC BY 4.0 授權，並包含以下要素：
1. 版權所有者與年份。
2. 授權條款 (Creative Commons Attribution 4.0 International License)。
3. 要求標示作者、來源連結。
4. 提供一個清晰的引用範例。
模組 11｜版本／更新頻率訊號
為 Sources 文件生成版本與更新頻率的元數據 (metadata)：
* Version (ver)：[例如：2.1]
* Last Updated (updated)：[今天的日期]
* Next Review (next-review)：[預計三個月後的日期]
* Frequency：[例如：Quarterly]
模組 12｜信任資產微件 (Trust Asset Widget)
從 Sources 或您的背景知識中，整理出 3-5 項可增強信任度的資產，並將其格式化為一個微件 (Widget) 的文字內容。
* 標題：我們的專業保證
* 資產 1 (例如：媒體報導)：[媒體名稱] 專題報導：「[報導標題]」
* 資產 2 (例如：獎項榮譽)：榮獲 [年份] [獎項名稱]
* 資產 3 (例如：演講/發表)：於 [會議名稱] 發表主題演講
模組 13｜Q-List 對映
從 Sources 建 50 條高意圖問句（含長尾、比較、替代、風險），逐條生成 25–40 字直球答案 + 100–150 字延伸解釋 + 引文；將未命中/信心低者標記回補內容。
模組 14｜Canonical 深連結
掃描 Sources，找出 5-8 個核心主張 (Claim) 或關鍵定義。為每一個主張創建一個 claim-id (例如 claim-performance-boost)，並生成對應的頁內錨點連結 (例如 [頁面URL]#claim-performance-boost)。
模組 15｜ImageObject 標記
為 Sources 中描述的每一張圖片，生成結構化資料標記屬性：
* Image URL: [圖片的網址]
* @type: ImageObject
* creator: [創作者姓名或組織]
* datePublished: [發布日期]
* caption: [圖片的說明文字]
* license: [圖片的授權條款 URL]
模組 16｜VideoObject＋逐字稿＋時間碼＋Speakable
假設 Sources 是一份影片的內容，請執行：
1. 生成 VideoObject Schema 屬性：name, description, uploadDate, thumbnailUrl。
2. 生成逐字稿 (Transcript)：將內容轉換為純文字稿。
3. 加上時間碼 (Timestamp)：在逐字稿的關鍵段落開頭加上 [HH:MM:SS]。
4. 標記 Speakable 段落：在最適合語音播報的段落前後，加上 <!--speakable-start--> 和 <!--speakable-end--> 標籤。
模組 17｜GBP Q&A 同步
從 Sources 中，提煉出 2-3 個最適合發布到 Google Business Profile (GBP) 問答區的內容。每個內容都包含：
* 問題 (Question)：一個直接、簡潔的客戶常見問題。
* 答案 (Answer)：一段 40-60 字、包含核心關鍵字且友善的回答。
模組 18｜Evidence Thread 四連貼
選擇 Sources 中一個最強力的核心主張，將其拆解成一個適合在社群媒體 (如 X/Twitter) 上發布的四連貼 (Thread)：
1. 貼文 1 (主張)：清晰地陳述核心主張。
2. 貼文 2 (數據)：提供支持主張的關鍵數據。
3. 貼文 3 (圖表)：用文字描述一張能證明此數據的圖表。
4. 貼文 4 (來源)：附上原始來源連結，並 @ 提及相關作者或機構。
模組 19｜ClaimReview
將 Sources 中的一個核心可驗證主張，轉換為 ClaimReview 結構化資料的格式：
* claimReviewed: [要審查的主張]
* author: [主張的提出者]
* reviewRating:
   * @type: Rating
   * ratingValue: [例如：True 或 False]
   * bestRating: True
   * worstRating: False
* itemReviewed:
   * @type: CreativeWork
   * author: [原作者資訊]
模組 20｜多語可引句變體
選出 Sources 中 3 句最關鍵、最適合被引用的句子 (Quote)。為每一句生成三種語言版本：
1. 繁體中文 (Traditional Chinese)
2. 英文 (English)
3. 簡體中文 (Simplified Chinese)
模組 21｜Micro-Dataset
將 Sources 中包含的數據或列表，轉換成一個微型數據集 (Micro-Dataset)。提供兩種可下載的格式：
1. CSV 格式：將數據轉換為逗號分隔值。
2. JSON 格式：將數據轉換為 JSON 物件陣列。
模組 22｜DataSet JSON-LD
為上一個模組創建的 Micro-Dataset，生成 DataSet 的 JSON-LD 結構化資料標記。包含以下關鍵屬性：
* @type: Dataset
* name: [數據集名稱]
* description: [數據集描述]
* measurementTechnique: [數據收集方法]
* temporalCoverage: [數據涵蓋的時間範圍，YYYY-MM-DD/YYYY-MM-DD]
* license: [數據的授權條款 URL]
模組 23｜Tool Card / Action JSON-LD
如果 Sources 描述了一個工具或一個可執行的流程，為其創建一個 Action JSON-LD 標記。
* 定義一個 potentialAction：
   * @type: [例如 SearchAction 或自定義 Action]
   * target:
      * @type: EntryPoint
      * urlTemplate: [執行的 URL 範本，例如 https://example.com/search?q={query}]
   * query-input: "required name=query"
模組 24｜反駁框 (Counter-position)
針對 Sources 中的主要論點，設想一個最常見的反對意見或替代方案，並撰寫一個「反駁框」：
1. 常見誤解/反方論點：簡潔陳述反對意見。
2. 我們的觀點/證據：用 Sources 中的證據，提出 2-3 點來回應或修正這個反對意見。
模組 25｜變體測試 (Tone Variation)
將 Sources 的核心摘要，改寫成三種不同的語氣版本，每種約 50-70 字：
1. 專業學術 (Professional)：使用精確、客觀的術語。
2. 口語對話 (Conversational)：使用第一人稱，語氣親切，像在和朋友解釋。
3. 媒體報導 (Journalistic)：使用引人注目的開頭，結構清晰，適合新聞稿。
模組 26｜意圖階段映射 (Intent Stage Mapping)
分析 Sources 內容，判斷它最符合哪個使用者意圖階段 (TOFU/MOFU/BOFU)，並為其設計對應的行動呼籲 (Call-to-Action)。
* 意圖階段：[TOFU - 認知 / MOFU - 考慮 / BOFU - 決策]
* 內容目標：[例如：教育讀者、比較方案、促成轉換]
* 建議 CTA：[例如：下載白皮書、預約諮詢、立即購買]
模組 27｜反事實修正庫 (Counterfactual Correction)
根據 Sources 的正確資訊，設想 3 個使用者可能會有的常見錯誤認知或 AI 可能會產生的「幻覺」答案。為每個錯誤答案提供一個標準的修正答案。
* 常見錯誤：[一句話描述錯誤的觀念]
* 標準答案：[根據 Sources 提供的 20-30 字正確解釋]
模組 28｜Linkless 守門 (Linkless Mention Repair)
掃描 Sources，找出其中提及的品牌或概念，但「沒有」附上超連結的詞語。列出這些詞語，並建議應該連結到的權威 URL。
模組 29｜ACR 致謝／修復回路 (Acknowledgement/Correction)
模擬一個情境：有第三方引用了 Sources 的內容但出現了小錯誤。請草擬一封簡短、友善的訊息，用於聯繫對方：
1. 感謝對方的引用。
2. 溫和地指出錯誤之處。
3. 提供正確的資訊和來源連結。
4. 表達未來合作的意願。
模組 30｜Voice Readability Gate
從 Sources 中提取一段約 100-120 字的核心段落。評估其「語音可讀性」，並提出 2-3 點修改建議，使其更適合 TTS (Text-to-Speech) 朗讀。修改建議可包含：簡化長句、解釋縮寫、加入語氣停頓提示。
模組 31｜模型偏好 A/B Matrix
從 Sources 中提取一句核心論述。基於這句論述，生成兩種不同句型結構的版本 (例如：主動語態 vs. 被動語態；數據在前 vs. 結論在前)，以便進行 A/B 測試，觀察哪種句型更容易被 AI 模型引用。
* 版本 A：
* 版本 B：
模組 32｜引用 Prompt-kit
為希望引用你 Sources 內容的第三方，設計一個「引用提示範本 (Prompt-kit)」。 「當你需要關於 [本文主題] 的權威資料時，可以使用以下提示： 根據 [你的品牌/作者名] 在 [文章標題] 中提供的數據，總結一下關於 [特定子題] 的核心觀點與關鍵數據。請務必附上來源：[本文的 Canonical URL]」
模組 33｜UTM for AI 來源分流
為 Sources 的主要連結，生成四個帶有 UTM 參數的版本，以便追蹤來自不同 AI 模型的流量：
* For Gemini: [URL]?utm_source=google&utm_medium=ai&utm_campaign=gemini_citation
* For ChatGPT: [URL]?utm_source=openai&utm_medium=ai&utm_campaign=chatgpt_citation
* For Perplexity: [URL]?utm_source=perplexity&utm_medium=ai&utm_campaign=perplexity_citation
* For Copilot: [URL]?utm_source=microsoft&utm_medium=ai&utm_campaign=copilot_citation
模組 34｜IndexNow／WebSub
為 Sources 內容的更新，生成一個 IndexNow 的 API 請求範例。 POST https://api.indexnow.org/indexnowContent-Type: application/json; charset=utf-8 { "host": "[你的網站主機名]", "key": "[你的 IndexNow 金鑰]", "keyLocation": "[你的金鑰文件 URL]", "urlList": [ "[你更新的文章 URL]" ] }
模組 35｜Speakable Sitemap
基於 Sources 中所有可朗讀的內容 (標有 Speakable 或 Q&A 格式)，為其創建一個 Sitemap XML 的片段，專門用於索引 speakable 內容。
<url>
  <loc>[頁面 URL]</loc>
  <lastmod>[YYYY-MM-DD]</lastmod>
  <xhtml:link
     rel="alternate"
     hreflang="[語言代碼]"
     href="[頁面 URL]"/>
  <n:news>
    ...
  </n:news>
  <speakable>
    <cssSelector>#speakable-q1, #speakable-a1</cssSelector>
  </speakable>
</url>


模組 36｜Q/A Micro-API
將 Sources 中的 FAQ 內容，轉換成一個模擬的 JSON API 回應格式。每個問答對都有一個唯一的 claim_id。 GET /api/answer?id=faq-performance-01
{
  "id": "faq-performance-01",
  "question": "這個方法能提升多少效能？",
  "short_answer": "根據我們的測試，平均可提升 15-20% 的處理速度。",
  "source": {
    "title": "文章標題",
    "url": "[文章 URL]#claim-performance-boost"
  }
}


請依此格式生成 3-5 組 API 回應範例。
模組 37｜KG Feed (Knowledge Graph Feed)
將 Sources 的核心實體與關係，轉換為一個知識圖譜 (Knowledge Graph) 的 JSON Feed (/kg.json) 片段。
{
  "entities": [
    { "@id": "entity1", "name": "核心概念A", "type": "Concept" },
    { "@id": "entity2", "name": "相關技術B", "type": "Technology" }
  ],
  "relations": [
    { "subject": "entity1", "predicate": "uses_technology", "object": "entity2" }
  ],
  "claims": [
    { "id": "claim_id_123", "text": "核心概念A能提升效率", "evidence_url": "[URL]" }
  ]
}


模組 38｜社群→文檔自動縫合
設計一個自動化流程的文字描述，將一系列相關的社群貼文 (例如一個 Twitter Thread) 縫合成一份完整的技術文件。
1. 觸發 (Trigger)：當系列貼文的最後一則被標記為 #thread_complete。
2. 擷取 (Fetch)：使用 API 抓取該 Thread 的所有貼文內容。
3. 轉換 (Convert)：將每則貼文轉換為 Markdown 格式，圖片轉為 ![alt](url)。
4. 增強 (Enhance)：自動加入 YAML front matter (title, date, author) 和 JSON-LD Schema。
5. 輸出 (Output)：生成 .md 檔案，並自動渲染成 PDF。
模組 39｜再散佈 (Syndication & Re-distribution)
當 AI 模型 (如 Gemini, Perplexity) 已經成功引用了 Sources 的內容後，設計一個「再散佈」策略：
1. 監控 (Monitor)：找到 AI 生成的答案頁面 URL。
2. 截圖 (Screenshot)：擷取引用了你內容的畫面。
3. 二次擴散 (Amplify)：將截圖發布到社群媒體，並附上：
   * 作者短評：(例如：「很高興我們的研究能被 Gemini 採用...」)
   * 原始文章連結：引導流量回源頭。
   * 感謝與標記：@ 提及該 AI 工具。
模組 40｜聯合發佈 (Co-citation)
為 Sources 的內容，規劃一個「聯合發佈」策略。列出 2-3 個潛在的合作對象，並說明合作方式。
* 合作對象 1 (學術/研究機構)：共同發表研究，互相引用對方的發現。
* 合作對象 2 (產業媒體)：邀請媒體撰寫基於此內容的深度報導，並在文章中建立權威連結。
* 合作對象 3 (技術夥伴)：在彼此的官方文件中，引用對方的解決方案作為推薦。
模組 41｜Evidence Heatmap 儀表
設計一個概念性的「證據熱力圖 (Evidence Heatmap)」儀表板的文字規格，用來追蹤內容被 AI 引用的成效。
* 追蹤指標：
   * 引用密度 (Citation Density)：哪些段落最常被引用？
   * 來源多樣性 (Source Diversity)：被多少種不同的 AI 模型引用？
   * 答案保留率 (Answer Retention)：AI 生成的答案在多久後還保留著你的引文？
* 可視化方式：在原文上以不同顏色深淺標示引用熱度。
模組 42｜Error Budget for Claims
為 Sources 中的所有核心主張，建立一個「錯誤預算 (Error Budget)」管理概念：
1. 定義閾值：設定一個可接受的「輕微不精確」或「過時」主張的百分比 (例如 5%)。
2. 監控：定期審核內容，標記出不再完全準確的主張。
3. 修復衝刺 (Correction Sprint)：當錯誤主張的比例超過閾值時，立即啟動一個內容更新衝刺，優先修正這些被標記的問題。