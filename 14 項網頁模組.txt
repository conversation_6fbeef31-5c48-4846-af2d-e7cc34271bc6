﻿模組 1｜聲明頁 (Source-of-Truth)
根據 Sources，為這份文件建立一個「Source-of-Truth」區塊。包含以下元素：
1. Canonical URL：[請填寫本文的標準網址]
2. Last Modified：[請填寫最後更新日期]
3. ETag：[請生成一個唯一的內容標識符，例如 W/"[內容的雜湊值]"]
4. Changelog：簡要條列本文 2-3 次最重要的更新歷史與日期。
模組 2｜Citation Snippet 卡
針對 Sources 中的每一個 H2 標題，執行以下操作：
1. 提煉出該段落最重要的 3 個核心要旨，以條列式呈現。
2. 根據這 3 個要旨，撰寫一段 25–40 字的精簡摘要。
3. 標示出生成此摘要最主要的 2–3 個來源 (Source 文件名)。
模組 3｜AIO Block (AI Optimization Block)
為整份 Sources 內容，生成一個 15 秒內可快速理解的三欄式摘要：
* 定義 (Definition)：用一句話解釋核心主題是什麼。
* 流程/結構 (Process/Structure)：用 3-4 個步驟或組成部分，說明它是如何運作或構成的。
* 關鍵數據 (Key Data)：提供 1-2 個最引人注目的數據點或事實。
模組 4｜FAQ 連發
根據 Sources 的核心主題，生成一個包含 8–12 個常見問題 (FAQ) 的列表。每個問題的答案都必須：
1. 直接、明確地回答問題。
2. 長度控制在 30 字以內。
3. 每個答案都需註明來源文件。
模組 5｜Speakable Pair (可朗讀語音對)
從 Sources 中找出至少 2 組問答內容，改寫成適合語音朗讀的格式。每一組都包含：
1. 問題 (Question)：一個清晰、口語化的問題。
2. 答案 (Answer)：一段 40-60 字、易於聽懂的答案，避免複雜的術語和數據。
模組 6｜可視化證據 (Visual Evidence)
找出 Sources 中最關鍵的一個數據或概念，並為其設計一個「圖卡」的文字描述：
1. 圖卡標題：吸引人的標題。
2. 核心資訊：最關鍵的數字或一句話結論。
3. 一句可引說明 (Quoteable Caption)：一句約 20 字的說明，可直接被引用。
4. 自然語言 ALT 描述：為視障用戶描述這張圖卡的內容與功能。
模組 7｜Author Graph 對齊
根據 Sources 的作者資訊，生成以下內容：
1. 作者名片 (Author Bio)：一段 30-50 字的作者簡介，包含姓名、職稱、專長領域。
2. Author Schema 屬性：
   * @type: Person
   * name: [作者姓名]
   * jobTitle: [職稱]
   * knowsAbout: [專長領域的 3-5 個關鍵字]
   * sameAs: [作者的社群媒體或個人網站連結，至少 2 個]
模組 8｜Entity sameAs (實體對齊)
掃描 Sources，列出其中提到的所有「品牌、作者、產品」等核心實體 (Entity)，並為每個實體找到其對應的官方網站、維基百科或權威資料來源的 URL，以 sameAs 格式呈現。
範例：
* Google: https://en.wikipedia.org/wiki/Google
* Gemini: https://gemini.google.com/
模組 9｜NAP／品牌信號 Beacon
根據 Sources 及您的背景知識，生成一組跨平台一致的 NAP (Name, Address, Phone) 與品牌信號，包含：
* 品牌/組織名稱：
* 主要業務描述 (25 字內)：
* 官方網站：
* 聯絡電話：
* 公司地址：
* 一致的社群媒體 Handle：
模組 10｜版權與重用條款
為 Sources 的內容，草擬一段版權與重用條款聲明。建議使用 CC BY 4.0 授權，並包含以下要素：
1. 版權所有者與年份。
2. 授權條款 (Creative Commons Attribution 4.0 International License)。
3. 要求標示作者、來源連結。
4. 提供一個清晰的引用範例。
模組 11｜版本／更新頻率訊號
為 Sources 文件生成版本與更新頻率的元數據 (metadata)：
* Version (ver)：[例如：2.1]
* Last Updated (updated)：[今天的日期]
* Next Review (next-review)：[預計三個月後的日期]
* Frequency：[例如：Quarterly]
模組 12｜信任資產微件 (Trust Asset Widget)
從 Sources 或您的背景知識中，整理出 3-5 項可增強信任度的資產，並將其格式化為一個微件 (Widget) 的文字內容。
* 標題：我們的專業保證
* 資產 1 (例如：媒體報導)：[媒體名稱] 專題報導：「[報導標題]」
* 資產 2 (例如：獎項榮譽)：榮獲 [年份] [獎項名稱]
* 資產 3 (例如：演講/發表)：於 [會議名稱] 發表主題演講


模組 14｜Canonical 深連結
掃描 Sources，找出 5-8 個核心主張 (Claim) 或關鍵定義。為每一個主張創建一個 claim-id (例如 claim-performance-boost)，並生成對應的頁內錨點連結 (例如 [頁面URL]#claim-performance-boost)。
模組 15｜ImageObject 標記
為 Sources 中描述的每一張圖片，生成結構化資料標記屬性：
* Image URL: [圖片的網址]
* @type: ImageObject
* creator: [創作者姓名或組織]
* datePublished: [發布日期]
* caption: [圖片的說明文字]
* license: [圖片的授權條款 URL]