<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NotebookLM × AISO 360™ 企業級整合方案</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700;900&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <!-- Highcharts -->
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/accessibility.js"></script>
    
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --secondary-blue: #1e40af;
            --accent-gold: #f59e0b;
            --light-blue: #eff6ff;
            --dark-text: #1f2937;
            --gray-text: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: var(--dark-text);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            scroll-behavior: smooth;
        }

        .slide {
            min-height: 100vh;
            width: 100%;
            padding: 80px 0;
            position: relative;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .slide.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .slide-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
        }

        /* Header Styles */
        .main-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .main-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .main-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
            position: relative;
            z-index: 2;
        }

        .main-subtitle {
            font-family: 'Crimson Text', serif;
            font-size: 1.5rem;
            font-style: italic;
            margin-bottom: 2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .company-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 25px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        /* Section Headers */
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-number {
            display: inline-block;
            background: var(--accent-gold);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 60px;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-family: 'Crimson Text', serif;
            font-size: 1.3rem;
            color: var(--gray-text);
            font-style: italic;
        }

        /* Content Cards */
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(30, 58, 138, 0.1);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
        }

        .card-icon {
            display: inline-block;
            background: var(--light-blue);
            color: var(--primary-blue);
            width: 60px;
            height: 60px;
            border-radius: 12px;
            text-align: center;
            line-height: 60px;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .card-content {
            color: var(--gray-text);
            line-height: 1.8;
        }

        /* AISO Framework Grid */
        .aiso-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .aiso-module {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
            border-top: 4px solid;
            transition: all 0.3s ease;
        }

        .aiso-module:hover {
            transform: translateY(-3px);
        }

        .aio { border-top-color: #06b6d4; }
        .geo { border-top-color: #10b981; }
        .vso { border-top-color: #f59e0b; }
        .avo { border-top-color: #8b5cf6; }

        .module-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .module-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
            color: var(--primary-blue);
        }

        .module-desc {
            color: var(--gray-text);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* KPI Dashboard */
        .kpi-dashboard {
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            margin: 2rem 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .kpi-item {
            text-align: center;
            padding: 1.5rem;
            background: var(--light-blue);
            border-radius: 12px;
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .kpi-label {
            font-size: 0.9rem;
            color: var(--gray-text);
            font-weight: 500;
        }

        /* Timeline */
        .timeline {
            position: relative;
            padding: 2rem 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--accent-gold);
            transform: translateX(-50%);
        }

        .timeline-item {
            display: flex;
            margin-bottom: 3rem;
            align-items: center;
        }

        .timeline-item:nth-child(even) {
            flex-direction: row-reverse;
        }

        .timeline-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
            width: 45%;
            position: relative;
        }

        .timeline-marker {
            width: 20px;
            height: 20px;
            background: var(--accent-gold);
            border-radius: 50%;
            margin: 0 2rem;
            border: 4px solid white;
            box-shadow: 0 0 0 3px var(--accent-gold);
        }

        .day-badge {
            display: inline-block;
            background: var(--primary-blue);
            color: white;
            padding: 0.3rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 0.8rem;
        }

        /* Code/JSON blocks */
        .code-block {
            background: #1f2937;
            color: #f3f4f6;
            padding: 2rem;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            overflow-x: auto;
            margin: 1.5rem 0;
        }

        .code-title {
            color: var(--accent-gold);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        /* Navigation */
        .nav-dots {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
        }

        .nav-dot {
            display: block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(30, 58, 138, 0.3);
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active,
        .nav-dot:hover {
            background: var(--primary-blue);
            transform: scale(1.2);
        }

        /* Charts */
        .chart-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-title { font-size: 2.5rem; }
            .section-title { font-size: 2rem; }
            .slide-container { padding: 0 20px; }
            .timeline::before { left: 20px; }
            .timeline-item { flex-direction: column !important; text-align: center; }
            .timeline-content { width: 100%; margin-left: 0; }
            .nav-dots { display: none; }
        }

        /* Animation delays */
        .delay-1 { animation-delay: 0.1s; }
        .delay-2 { animation-delay: 0.2s; }
        .delay-3 { animation-delay: 0.3s; }
        .delay-4 { animation-delay: 0.4s; }
    </style>
</head>
<body>

<!-- Navigation Dots -->
<div class="nav-dots">
    <span class="nav-dot active" data-slide="0"></span>
    <span class="nav-dot" data-slide="1"></span>
    <span class="nav-dot" data-slide="2"></span>
    <span class="nav-dot" data-slide="3"></span>
    <span class="nav-dot" data-slide="4"></span>
    <span class="nav-dot" data-slide="5"></span>
    <span class="nav-dot" data-slide="6"></span>
    <span class="nav-dot" data-slide="7"></span>
    <span class="nav-dot" data-slide="8"></span>
</div>

<!-- Slide 1: Title -->
<section class="slide main-header visible" id="slide-0">
    <div class="slide-container">
        <h1 class="main-title">NotebookLM × AISO 360™</h1>
        <p class="main-subtitle">Enterprise Integration Strategy</p>
        <p class="lead text-white-50 mb-4">策略→戰術→流程→模板→風險管控→下一步<br>全部一次到位，直接可上線</p>
        <div class="company-badge">
            <i class="bi bi-building me-2"></i>
            SEO優化王 × 柏瀚國際 AISO 360™ 戰略框架
        </div>
    </div>
</section>

<!-- Slide 2: Strategy Overview -->
<section class="slide" id="slide-1">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">01</div>
            <h2 class="section-title">內容創作策略</h2>
            <p class="section-subtitle">From Articles to Multi-modal Output</p>
        </div>

        <div class="content-card">
            <div class="card-icon"><i class="bi bi-bullseye"></i></div>
            <h3 class="card-title">核心目標</h3>
            <div class="card-content">
                以 Google NotebookLM 的來源扎根（source-grounded）特性，驅動 AISO 360™（AIO/GEO/VSO/AVO）產線，
                批量生成可被引用、可追溯、可行動（Actionable）的內容資產。
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-archive"></i></div>
                    <h3 class="card-title">來源即資產</h3>
                    <div class="card-content">
                        以官網 SoT、案例白皮書、作者頁、GBP、媒體報導為「Notebook Sources」，
                        產出 Audio/Video Overviews、Briefing、FAQ、腳本，均附來源內嵌引文。
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-layers"></i></div>
                    <h3 class="card-title">多模態合成</h3>
                    <div class="card-content">
                        利用 NotebookLM 的音訊總覽與影片總覽，把單一文章轉換為 90 秒摘要音檔、
                        3-7 分鐘簡報型影片 + 逐字稿 + 可讀段落。
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Slide 3: AISO Framework -->
<section class="slide" id="slide-2">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">02</div>
            <h2 class="section-title">AISO 360™ 框架架構</h2>
            <p class="section-subtitle">Comprehensive AI Search Optimization</p>
        </div>

        <div class="aiso-grid">
            <div class="aiso-module aio">
                <div class="module-icon">🎯</div>
                <h3 class="module-title">AIO - AI 摘要優化</h3>
                <div class="module-desc">
                    Audio/Video Overview + Briefing + 一頁式 Executive Summary
                </div>
            </div>
            <div class="aiso-module geo">
                <div class="module-icon">🔍</div>
                <h3 class="module-title">GEO - 生成引擎覆蓋</h3>
                <div class="module-desc">
                    Notebook Guide→FAQ→Comparisons→Counter-arguments；以 Discover 擴充外部權威
                </div>
            </div>
            <div class="aiso-module vso">
                <div class="module-icon">🗣️</div>
                <h3 class="module-title">VSO - 語音搜尋</h3>
                <div class="module-desc">
                    生成 speakable 片段與 FAQ；同步 GBP Q&A
                </div>
            </div>
            <div class="aiso-module avo">
                <div class="module-icon">🤖</div>
                <h3 class="module-title">AVO - Agent 可見度</h3>
                <div class="module-desc">
                    HowTo/Action/Service/Person/Org Schema 打包，保證可抽取、可行動、可歸因
                </div>
            </div>
        </div>

        <div class="chart-container">
            <h4 class="text-center mb-4">AISO 360™ 模組整合流程</h4>
            <div id="aisoFlowChart" style="height: 400px;"></div>
        </div>
    </div>
</section>

<!-- Slide 4: KPI Dashboard -->
<section class="slide" id="slide-3">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">03</div>
            <h2 class="section-title">核心 KPI 指標</h2>
            <p class="section-subtitle">Key Performance Indicators & Governance</p>
        </div>

        <div class="kpi-dashboard">
            <h4 class="text-center mb-4">AISO 360™ 成效追蹤儀表板</h4>
            <div class="kpi-grid">
                <div class="kpi-item">
                    <div class="kpi-value">85%</div>
                    <div class="kpi-label">ACR<br>助理引用率</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value">72%</div>
                    <div class="kpi-label">GEO_COV<br>問題命中率</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value">68%</div>
                    <div class="kpi-label">VSO_HIT<br>語音命中率</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value">45%</div>
                    <div class="kpi-label">CS_SHARE<br>可引用片段占比</div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5>月度績效趨勢</h5>
                    <div id="performanceChart" style="height: 300px;"></div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-shield-check"></i></div>
                    <h4 class="card-title">治理機制</h4>
                    <div class="card-content">
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>來源清單 + 逐句引文</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>外部事實驗證對齊</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Evidence Log 提交</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>20% 段落人工抽檢</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Slide 5: Tactical Modules -->
<section class="slide" id="slide-4">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">04</div>
            <h2 class="section-title">戰術模組</h2>
            <p class="section-subtitle">Ready-to-use NotebookLM Prompt Modules</p>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-file-text"></i></div>
                    <h3 class="card-title">模組 A | Executive Brief</h3>
                    <div class="card-content">
                        生成 30 秒音訊摘要 + 150 字文字摘要 + 三點要旨（含引文），
                        並輸出 2 個「speakable」段落，最後列出可行動 CTA。
                    </div>
                </div>
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-question-circle"></i></div>
                    <h3 class="card-title">模組 B | Q-list → GEO 測試</h3>
                    <div class="card-content">
                        建立 50 條高意圖問句，生成 25-40 字直球答案 + 延伸解釋 + 引文，
                        標記未命中項目供回補。
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-play-circle"></i></div>
                    <h3 class="card-title">模組 C | Audio/Video Overview</h3>
                    <div class="card-content">
                        產出「Brief」「Deep Dive」「Critique」「Debate」四款音訊總覽，
                        要求首段點名品牌、每段附來源、結尾 CTA。
                    </div>
                </div>
                <div class="content-card">
                    <div class="card-icon"><i class="bi bi-database"></i></div>
                    <h3 class="card-title">模組 D | Evidence Pack</h3>
                    <div class="card-content">
                        匯總原始數據/案例/權威研究三類證據，
                        以 CSV/JSON 並存，提供引用優先級權重。
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Slide 6: SOP Workflow -->
<section class="slide" id="slide-5">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">05</div>
            <h2 class="section-title">標準作業流程</h2>
            <p class="section-subtitle">Standardized Operating Procedure</p>
        </div>

        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-content">
                    <h5><i class="bi bi-folder text-primary me-2"></i>建置來源池</h5>
                    <p>SoT（品牌頁/作者頁/服務頁/案例/白皮書/媒體）上傳為 Notebook Sources，啟用 Discover 補權威來源</p>
                </div>
                <div class="timeline-marker"></div>
                <div style="width: 45%;"></div>
            </div>

            <div class="timeline-item">
                <div style="width: 45%;"></div>
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5><i class="bi bi-lightbulb text-warning me-2"></i>Notebook Guide 起手式</h5>
                    <p>生成 Executive Brief、FAQ、Comparison、Counter-claims，所有段落強制內嵌引文</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <h5><i class="bi bi-camera-video text-info me-2"></i>多模態輸出</h5>
                    <p>生成 Audio/Video Overviews + 腳本，擬出社群 8-12 條可引用卡</p>
                </div>
                <div class="timeline-marker"></div>
                <div style="width: 45%;"></div>
            </div>

            <div class="timeline-item">
                <div style="width: 45%;"></div>
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5><i class="bi bi-code-square text-success me-2"></i>結構化封裝</h5>
                    <p>每篇內容寫入 JSON-LD（Org/Person/Service/FAQ/Video/HowTo/Action/Speakable）</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <h5><i class="bi bi-shield-check text-danger me-2"></i>審核與門檻</h5>
                    <p>EVID_DENS≥3/要點、CS_SHARE≥30%、VSO 朗讀 20-30 秒規範</p>
                </div>
                <div class="timeline-marker"></div>
                <div style="width: 45%;"></div>
            </div>

            <div class="timeline-item">
                <div style="width: 45%;"></div>
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5><i class="bi bi-broadcast text-primary me-2"></i>發佈與追蹤</h5>
                    <p>官網/GBP/社群/Newsletter 同步，以 Looker Studio 追蹤 KPI</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <h5><i class="bi bi-arrow-repeat text-secondary me-2"></i>迭代優化</h5>
                    <p>對未命中問句回補內容，重測音訊/影片，持續優化</p>
                </div>
                <div class="timeline-marker"></div>
                <div style="width: 45%;"></div>
            </div>
        </div>
    </div>
</section>

<!-- Slide 7: JSON-LD Templates -->
<section class="slide" id="slide-6">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">06</div>
            <h2 class="section-title">結構化資料範本</h2>
            <p class="section-subtitle">JSON-LD Schema Templates</p>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="code-block">
                    <div class="code-title">🏢 Organization Schema</div>
                    <pre><code>{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "柏瀚國際科技有限公司",
  "alternateName": [
    "SEO 優化王",
    "SEOKING"
  ],
  "url": "https://seoking.com.tw",
  "telephone": "+886-2-2563-4727",
  "email": "<EMAIL>"
}</code></pre>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="code-block">
                    <div class="code-title">❓ FAQ Schema</div>
                    <pre><code>{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [{
    "@type": "Question",
    "name": "什麼是 AISO 360™？",
    "acceptedAnswer": {
      "@type": "Answer",
      "text": "AISO 360™ 是結合 AIO/GEO/VSO/AVO 的 AI 搜尋優化框架"
    }
  }]
}</code></pre>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="code-block">
                    <div class="code-title">🎥 Video Schema</div>
                    <pre><code>{
  "@context": "https://schema.org",
  "@type": "VideoObject",
  "name": "AISO 360™ 三分鐘總覽",
  "duration": "PT3M30S",
  "publisher": {
    "@id": "https://seoking.com.tw/#org"
  },
  "inLanguage": "zh-Hant"
}</code></pre>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="code-block">
                    <div class="code-title">🔗 Action Schema</div>
                    <pre><code>{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "potentialAction": {
    "@type": "ContactAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://seoking.com.tw/contact"
    },
    "result": {
      "name": "預約行銷諮詢"
    }
  }
}</code></pre>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Slide 8: 14-Day Sprint -->
<section class="slide" id="slide-7">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">07</div>
            <h2 class="section-title">14 天衝刺路線圖</h2>
            <p class="section-subtitle">Fast-track Implementation Plan</p>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="card-icon" style="background: rgba(6, 182, 212, 0.1); color: #0891b2;">
                        <i class="bi bi-calendar-week"></i>
                    </div>
                    <h3 class="card-title">第一週 (D1-D7)</h3>
                    <div class="timeline" style="padding: 1rem 0;">
                        <div class="day-badge">D1-D2</div>
                        <p><strong>整理 SoT</strong><br>案例/作者/GBP/社群精選 30 份為 Notebook Sources</p>
                        
                        <div class="day-badge">D3-D5</div>
                        <p><strong>建立 Notebook</strong><br>AISO 方法論 + 搬家案例線，跑 Guide + Discover</p>
                        
                        <div class="day-badge">D6-D7</div>
                        <p><strong>產出初始內容</strong><br>Exec Brief / 50 問 Q-list</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="card-icon" style="background: rgba(16, 185, 129, 0.1); color: #059669;">
                        <i class="bi bi-calendar-check"></i>
                    </div>
                    <h3 class="card-title">第二週 (D8-D14)</h3>
                    <div class="timeline" style="padding: 1rem 0;">
                        <div class="day-badge">D8-D10</div>
                        <p><strong>多模態輸出</strong><br>生成 Audio/Video Overview（各 2 版），補上逐字稿與引用清單</p>
                        
                        <div class="day-badge">D11-D12</div>
                        <p><strong>內容發布</strong><br>發布 2 篇主文 + 8-12 張 Citation 卡，上線 JSON-LD</p>
                        
                        <div class="day-badge">D13-D14</div>
                        <p><strong>測試與優化</strong><br>以 Q-list 對測，初始化 KPI 儀表板</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="chart-container mt-4">
            <h4 class="text-center mb-4">實施進度甘特圖</h4>
            <div id="ganttChart" style="height: 350px;"></div>
        </div>
    </div>
</section>

<!-- Slide 9: Contact & Next Steps -->
<section class="slide" id="slide-8">
    <div class="slide-container">
        <div class="section-header">
            <div class="section-number">08</div>
            <h2 class="section-title">聯繫我們</h2>
            <p class="section-subtitle">Ready to Transform Your AI Search Strategy</p>
        </div>

        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="content-card">
                    <h3 class="card-title text-center mb-4">開始您的 AISO 360™ 轉型之旅</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon me-3" style="width: 50px; height: 50px; line-height: 50px; font-size: 1.2rem;">
                                    <i class="bi bi-telephone"></i>
                                </div>
                                <div>
                                    <strong>專線諮詢</strong><br>
                                    <a href="tel:+886-2-2563-4727" class="text-decoration-none">+886-2-2563-4727</a>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon me-3" style="width: 50px; height: 50px; line-height: 50px; font-size: 1.2rem;">
                                    <i class="bi bi-envelope"></i>
                                </div>
                                <div>
                                    <strong>專業諮詢</strong><br>
                                    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon me-3" style="width: 50px; height: 50px; line-height: 50px; font-size: 1.2rem;">
                                    <i class="bi bi-geo-alt"></i>
                                </div>
                                <div>
                                    <strong>台北辦公室</strong><br>
                                    台北市中山區新生北路二段31之1號9樓之7
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon me-3" style="width: 50px; height: 50px; line-height: 50px; font-size: 1.2rem;">
                                    <i class="bi bi-globe"></i>
                                </div>
                                <div>
                                    <strong>官方網站</strong><br>
                                    <a href="https://seoking.com.tw" class="text-decoration-none" target="_blank">seoking.com.tw</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <a href="#" class="btn btn-primary btn-lg px-5 py-3">
                            <i class="bi bi-calendar-plus me-2"></i>預約專業諮詢
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="content-card text-center">
                    <h4 class="card-title">企業成果保證</h4>
                    <div class="kpi-item mb-3">
                        <div class="kpi-value text-primary">200+</div>
                        <div class="kpi-label">服務企業</div>
                    </div>
                    <div class="kpi-item mb-3">
                        <div class="kpi-value text-success">85%</div>
                        <div class="kpi-label">平均 AI 可見度提升</div>
                    </div>
                    <div class="kpi-item mb-3">
                        <div class="kpi-value text-info">60</div>
                        <div class="kpi-label">天見到初始成效</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Scroll-based slide visibility
function updateSlideVisibility() {
    const slides = document.querySelectorAll('.slide');
    const navDots = document.querySelectorAll('.nav-dot');
    
    slides.forEach((slide, index) => {
        const rect = slide.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight * 0.6 && rect.bottom > window.innerHeight * 0.4;
        
        if (isVisible) {
            slide.classList.add('visible');
            navDots[index]?.classList.add('active');
        } else {
            navDots[index]?.classList.remove('active');
        }
    });
}

// Navigation dots click handler
document.querySelectorAll('.nav-dot').forEach(dot => {
    dot.addEventListener('click', () => {
        const slideIndex = parseInt(dot.dataset.slide);
        const targetSlide = document.getElementById(`slide-${slideIndex}`);
        targetSlide.scrollIntoView({ behavior: 'smooth' });
    });
});

// Initialize charts when visible
function initializeCharts() {
    // AISO Flow Chart
    Highcharts.chart('aisoFlowChart', {
        chart: {
            type: 'column',
            backgroundColor: 'transparent'
        },
        title: {
            text: null
        },
        xAxis: {
            categories: ['內容來源', 'NotebookLM 處理', '多模態輸出', '結構化封裝', '發佈追蹤']
        },
        yAxis: {
            title: {
                text: '處理效率 (%)'
            }
        },
        colors: ['#1e3a8a', '#10b981', '#f59e0b', '#8b5cf6'],
        series: [{
            name: '自動化程度',
            data: [85, 95, 78, 82, 75],
            showInLegend: false
        }],
        credits: { enabled: false },
        legend: { enabled: false }
    });

    // Performance Chart
    Highcharts.chart('performanceChart', {
        chart: {
            type: 'line',
            backgroundColor: 'transparent'
        },
        title: {
            text: null
        },
        xAxis: {
            categories: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: {
            title: {
                text: '成效指標 (%)'
            }
        },
        colors: ['#1e3a8a', '#10b981', '#f59e0b', '#8b5cf6'],
        series: [{
            name: 'ACR',
            data: [65, 70, 75, 80, 82, 85]
        }, {
            name: 'GEO_COV',
            data: [55, 60, 65, 68, 70, 72]
        }, {
            name: 'VSO_HIT',
            data: [45, 52, 58, 62, 65, 68]
        }, {
            name: 'CS_SHARE',
            data: [30, 35, 38, 40, 42, 45]
        }],
        credits: { enabled: false }
    });

    // Gantt Chart
    Highcharts.chart('ganttChart', {
        chart: {
            type: 'xrange',
            backgroundColor: 'transparent'
        },
        title: {
            text: null
        },
        xAxis: {
            type: 'datetime'
        },
        yAxis: {
            title: {
                text: null
            },
            categories: ['整理 SoT', '建立 Notebook', '產出內容', '多模態輸出', '內容發布', '測試優化'],
            reversed: true
        },
        colors: ['#1e3a8a', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#f97316'],
        series: [{
            name: '任務進度',
            borderColor: 'gray',
            pointWidth: 20,
            data: [{
                x: Date.UTC(2025, 0, 1),
                x2: Date.UTC(2025, 0, 3),
                y: 0,
                name: 'D1-D2'
            }, {
                x: Date.UTC(2025, 0, 3),
                x2: Date.UTC(2025, 0, 6),
                y: 1,
                name: 'D3-D5'
            }, {
                x: Date.UTC(2025, 0, 6),
                x2: Date.UTC(2025, 0, 8),
                y: 2,
                name: 'D6-D7'
            }, {
                x: Date.UTC(2025, 0, 8),
                x2: Date.UTC(2025, 0, 11),
                y: 3,
                name: 'D8-D10'
            }, {
                x: Date.UTC(2025, 0, 11),
                x2: Date.UTC(2025, 0, 13),
                y: 4,
                name: 'D11-D12'
            }, {
                x: Date.UTC(2025, 0, 13),
                x2: Date.UTC(2025, 0, 15),
                y: 5,
                name: 'D13-D14'
            }],
            dataLabels: {
                enabled: true
            }
        }],
        credits: { enabled: false }
    });
}

// Event listeners
window.addEventListener('scroll', updateSlideVisibility);
window.addEventListener('load', () => {
    updateSlideVisibility();
    setTimeout(initializeCharts, 1000);
});

// Initialize first slide
document.addEventListener('DOMContentLoaded', () => {
    updateSlideVisibility();
});
</script>

</body>
</html>