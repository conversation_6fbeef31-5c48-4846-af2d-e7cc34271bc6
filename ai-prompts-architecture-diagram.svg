<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="2500" viewBox="0 0 1400 2500" xmlns="http://www.w3.org/2000/svg" style="display: block; margin: 0 auto; max-width: 100%; height: auto;">
  <defs>
    <!-- 漸層定義 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promptGradient1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promptGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promptGradient3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promptGradient4" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#c471f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fa71cd;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promptGradient5" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    
    <!-- 陰影效果 -->
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#0000001A"/>
    </filter>
  </defs>

  <!-- 主標題區域 -->
  <g>
    <rect width="1400" height="180" fill="url(#headerGradient)" filter="url(#shadow)"/>
    <rect x="20" y="20" width="1360" height="140" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 主標題 -->
    <text x="700" y="65" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="36" font-weight="bold" fill="#2d3748">🚀 10個進階 AI 提示詞架構流程圖</text>
    <text x="700" y="95" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="20" fill="#4a5568">基於《AI 搜尋指南》核心概念的實戰性 AI SEO 優化工具</text>
    <text x="700" y="125" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" fill="rgba(255,255,255,0.9)">SEO優化王 × 柏瀚國際 AISO 360™ 戰略框架</text>
    <text x="700" y="150" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="rgba(255,255,255,0.9)">涵蓋品牌曝光、意圖對齊、競品分析、技術實作與趨勢洞察</text>
  </g>

  <!-- 框架總覽 -->
  <g transform="translate(50, 200)">
    <rect width="1300" height="100" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="30" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">🎯 進階 AI 提示詞應用框架</text>
    <text x="650" y="55" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">五大分析層面 × 十大核心提示詞 × 實戰應用場景</text>
    <text x="650" y="80" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#667eea">LMO 四大原則 + AISG 評估維度 + AI Crawler 決策框架</text>
  </g>

  <!-- 第一行：提示詞 1-2 -->
  <g transform="translate(50, 320)">
    <!-- 提示詞一：品牌 AI 能見度快篩 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient1)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📋 提示詞一：品牌 AI 能見度快篩</text>
      <text x="310" y="60" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4a5568">AISG 基礎版</text>
      
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">🎯 目的：</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">快速了解品牌在 ChatGPT、Gemini、Perplexity、Claude</text>
      <text x="30" y="134" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">等 AI 搜尋結果中的曝光頻率、引用位置與情感分數</text>
      
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">📊 輸出格式：</text>
      <text x="30" y="184" font-family="'Courier New', monospace" 
            font-size="16" fill="#666">JSON: {engine, answer_position, citation, sentiment_score}</text>
      <text x="30" y="208" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">+ 80字內洞見摘要與下一步建議</text>
      
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">🔍 分析層面：</text>
      <text x="150" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#43e97b">品牌曝光 + 情感分析</text>
    </g>

    <!-- 提示詞二：AI 搜尋長尾意圖對齊診斷 -->
    <g transform="translate(650, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient2)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🔍 提示詞二：AI 搜尋長尾意圖對齊診斷</text>
      
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">🎯 目的：</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">比對「使用者真實意圖」與當前內容差距，找出高機會長尾詞</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">📊 執行步驟：</text>
      <text x="30" y="149" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">1. 列出前20個與目標主題最相關的長尾問句</text>
      <text x="30" y="173" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">2. 標記哪些問句在 AI Overviews 中已有摘要</text>
      <text x="30" y="197" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">3. 提出3條內容機會優先順序</text>
      
      <text x="30" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">🔍 分析層面：</text>
      <text x="150" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4facfe">使用者意圖 + 內容缺口</text>
    </g>
  </g>

  <!-- 第二行：提示詞 3-4 -->
  <g transform="translate(50, 620)">
    <!-- 提示詞三：結構化內容重塑 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient3)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🗂️ 提示詞三：結構化內容重塑</text>
      <text x="310" y="60" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4a5568">LMO 四大原則</text>
      
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">🎯 目的：</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">將現有長文重構成 AI 友善格式（FAQ、Bullet、引用）</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">🏗️ LMO 四大原則：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 對話清晰度：TLDR ≤100字</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 權威引用：結尾列出引用來源（URL）</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 長文結構化：3-5段主體，每段≤120字要點</text>
      <text x="30" y="220" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 意圖對齊：H2標題結構優化</text>
      
      <text x="30" y="250" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">🔍 分析層面：</text>
      <text x="150" y="250" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#fa709a">內容格式 + 語意清晰度</text>
    </g>

    <!-- 提示詞四：AI Crawler 開放/阻擋決策矩陣 -->
    <g transform="translate(650, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient4)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🕸️ 提示詞四：AI Crawler 決策矩陣</text>
      
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">🎯 目的：</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">協助評估站點是否允許 AI Bot 抓取</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">📊 評估維度：</text>
      <text x="30" y="145" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 內容類型：公開 / 付費牆 / 專屬數據</text>
      <text x="30" y="165" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 業務目標：曝光 / 轉換 / 品牌管控</text>
      
      <text x="30" y="195" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">🔧 輸出：</text>
      <text x="30" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">Pros &amp; Cons 視覺化表格 + robots.txt 範例</text>
      
      <text x="30" y="245" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">🔍 分析層面：</text>
      <text x="150" y="245" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#c471f5">風險評估 + 技術落地</text>
    </g>
  </g>

  <!-- 第三行：提示詞 5-6 -->
  <g transform="translate(50, 920)">
    <!-- 提示詞五：競品引用差距 X-Ray -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient5)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🏆 提示詞五：競品引用差距 X-Ray</text>
      
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffecd2">🎯 目的：</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">比較我與三大競品在 AI 回答中的引用排名與內容類型</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffecd2">📊 執行步驟：</text>
      <text x="30" y="145" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">1. 抓取各品牌於 Perplexity &amp; Gemini 過去30天引用數據</text>
      <text x="30" y="165" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">2. 引用內容分類（統計報告/如何文/新聞/評論）</text>
      <text x="30" y="185" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">3. 產出表格+洞見：三大缺失與最佳實踐</text>
      
      <text x="30" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffecd2">🔍 分析層面：</text>
      <text x="150" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#fcb69f">競爭對比 + 引用結構</text>
    </g>

    <!-- 提示詞六：社群聲量與情感監測 -->
    <g transform="translate(650, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient1)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">💬 提示詞六：社群聲量與情感監測</text>
      <text x="310" y="60" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4a5568">Reddit × Review Hub</text>
      
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">🎯 目的：</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">追蹤 Reddit/Yelp/Google Reviews 中品牌正負評與主題趨勢</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">📊 監測範圍：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">最近90天 Reddit + Yelp + Google Reviews</text>
      
      <text x="30" y="190" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">🔍 輸出格式：</text>
      <text x="30" y="210" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 正向/負向/中立詞雲 + 熱門議題Top5</text>
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 對SEO/LMO影響的兩項風險與兩項契機</text>
      
      <text x="30" y="260" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#43e97b">🔍 分析層面：</text>
      <text x="150" y="260" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#43e97b">品牌情感 + 多來源聚合</text>
    </g>
  </g>

  <!-- 第四行：提示詞 7-8 -->
  <g transform="translate(50, 1220)">
    <!-- 提示詞七：PR & 媒體曝光機會地圖 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient2)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📰 提示詞七：PR &amp; 媒體曝光機會地圖</text>
      
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">🎯 目的：</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">找出 AI 模型常引用之媒體，並擬定 PR 刊登計畫</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">📊 執行步驟：</text>
      <text x="30" y="145" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">1. 依AI模型合作出版商列表，列出Top10高權威媒體</text>
      <text x="30" y="165" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">2. 提議3則可投稿主題，含切入角度、記者聯絡方式</text>
      <text x="30" y="185" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">3. 定義KPI：AI回答引用次數、品牌提及排名提升</text>
      
      <text x="30" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#4facfe">🔍 分析層面：</text>
      <text x="150" y="215" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4facfe">權威背書 + 媒體策略</text>
    </g>

    <!-- 提示詞八：多格式內容整合 -->
    <g transform="translate(650, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient3)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🖼️ 提示詞八：多格式內容整合</text>
      <text x="310" y="60" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4a5568">文字 × 影片 × 圖像</text>
      
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">🎯 目的：</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">規劃跨格式素材以提升 Gemini/Perplexity 引用機率</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">📊 規劃內容：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 1篇長文 • 2張資訊圖 • 1支≤90秒短片</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• Schema結構化標記嵌入說明</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">• 發布時間表（Gantt簡表）</text>
      
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#fa709a">🔍 分析層面：</text>
      <text x="150" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#fa709a">多模態優化 + 排程</text>
    </g>
  </g>

  <!-- 第五行：提示詞 9-10 -->
  <g transform="translate(50, 1520)">
    <!-- 提示詞九：AI 搜尋 KPI 儀表板定義 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient4)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">📈 提示詞九：AI 搜尋 KPI 儀表板定義</text>
      
      <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">🎯 目的：</text>
      <text x="30" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">建立可追蹤 Brand Visibility、Sentiment、Citation Rate 等核心指標</text>
      
      <text x="30" y="125" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">📊 核心指標：</text>
      <text x="30" y="145" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">品牌曝光率 • Citation Rate • Position Score</text>
      <text x="30" y="165" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">每指標定義：計算公式、資料來源API、目標值設定邏輯</text>
      
      <text x="30" y="195" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">🔧 輸出格式：</text>
      <text x="30" y="215" font-family="'Courier New', monospace" 
            font-size="16" fill="#666">CSV: metric,definition,formula,data_source,target</text>
      
      <text x="30" y="245" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#c471f5">🔍 分析層面：</text>
      <text x="150" y="245" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#c471f5">績效度量 + 資料工程</text>
    </g>

    <!-- 提示詞十：持續優化循環 -->
    <g transform="translate(650, 0)">
      <rect x="0" y="0" width="620" height="280" rx="20" fill="url(#promptGradient5)" filter="url(#shadow)"/>
      <rect x="15" y="15" width="590" height="250" rx="15" fill="rgba(255,255,255,0.95)"/>
      
      <text x="310" y="40" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="20" font-weight="bold" fill="#2d3748">🔄 提示詞十：持續優化循環</text>
      <text x="310" y="60" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#4a5568">OODA for AI Search</text>
      
      <text x="30" y="90" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffecd2">🎯 目的：</text>
      <text x="30" y="110" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">建立「Observe→Orient→Decide→Act」週期化流程</text>
      
      <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffecd2">🔄 OODA循環：</text>
      <text x="30" y="160" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">1. 列出每月應收集之AI Search數據清單</text>
      <text x="30" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">2. 對照五大策略，示範一次OODA循環（表格）</text>
      <text x="30" y="200" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#666">3. 自動化建議（n8n/Zapier流程）</text>
      
      <text x="30" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" font-weight="bold" fill="#ffecd2">🔍 分析層面：</text>
      <text x="150" y="230" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
            font-size="16" fill="#fcb69f">策略循環 + 自動化落地</text>
    </g>
  </g>

  <!-- 使用指南 -->
  <g transform="translate(50, 1820)">
    <rect width="1300" height="320" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">🚀 如何使用這些 AI 提示詞</text>
    
    <g transform="translate(40, 60)">
      <!-- 第一欄 -->
      <g transform="translate(0, 0)">
        <rect x="0" y="0" width="400" height="240" rx="12" fill="rgba(67, 233, 123, 0.15)" stroke="#43e97b" stroke-width="2"/>
        <text x="20" y="30" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="18" font-weight="bold" fill="#2d3748">🔧 1. 替換變數</text>
        <text x="30" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">將 {品牌名稱}、{主題} 等換成實際資訊</text>
        <text x="30" y="80" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">範例：</text>
        <text x="30" y="100" font-family="'Courier New', monospace" 
              font-size="16" fill="#666">{品牌名稱} → "SEO優化王"</text>
        <text x="30" y="124" font-family="'Courier New', monospace" 
              font-size="16" fill="#666">{主題} → "AI搜尋優化"</text>
        <text x="30" y="148" font-family="'Courier New', monospace" 
              font-size="16" fill="#666">{描述} → "AISO360™ 解決方案"</text>
        
        <text x="20" y="180" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="18" font-weight="bold" fill="#2d3748">📊 2. 分段執行</text>
        <text x="30" y="205" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">依專案階段選用對應提示詞</text>
        <text x="30" y="225" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">可搭配 Markdown Mind-Map 輸出格式</text>
      </g>
      
      <!-- 第二欄 -->
      <g transform="translate(430, 0)">
        <rect x="0" y="0" width="400" height="240" rx="12" fill="rgba(79, 172, 254, 0.15)" stroke="#4facfe" stroke-width="2"/>
        <text x="20" y="30" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="18" font-weight="bold" fill="#2d3748">🔗 3. 串接工具</text>
        <text x="30" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">若需自動化，可將輸出 JSON/CSV</text>
        <text x="30" y="75" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">直接餵入自動化流程：</text>
        <text x="30" y="100" font-family="'Courier New', monospace" 
              font-size="16" fill="#666">n8n ↔ Notion ↔ BI Dashboard</text>
        
        <text x="20" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="18" font-weight="bold" fill="#2d3748">🎯 4. 實戰應用</text>
        <text x="30" y="165" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">把《AI 搜尋指南》評估邏輯轉化為</text>
        <text x="30" y="185" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">每日可執行的 AI SEO 實戰動作</text>
        <text x="30" y="205" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">全面提升品牌在AI搜尋生態中的</text>
        <text x="30" y="225" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" font-weight="bold" fill="#4facfe">能見度與權威度</text>
      </g>
      
      <!-- 第三欄 -->
      <g transform="translate(860, 0)">
        <rect x="0" y="0" width="400" height="240" rx="12" fill="rgba(250, 112, 154, 0.15)" stroke="#fa709a" stroke-width="2"/>
        <text x="20" y="30" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="18" font-weight="bold" fill="#2d3748">📈 5. 核心概念基礎</text>
        <text x="30" y="55" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">基於《AI 搜尋優化指南》核心概念：</text>
        <text x="30" y="80" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" font-weight="bold" fill="#fa709a">• LMO 四大原則</text>
        <text x="30" y="100" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" font-weight="bold" fill="#fa709a">• 五大最佳化策略</text>
        <text x="30" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" font-weight="bold" fill="#fa709a">• AI Crawler 決策框架</text>
        <text x="30" y="140" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" font-weight="bold" fill="#fa709a">• AISG 評估維度</text>
        
        <text x="30" y="175" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" fill="#666">設計方便 AI 使用者快速產出</text>
        <text x="30" y="195" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
              font-size="16" font-weight="bold" fill="#fa709a">具體、可操作的 AI SEO 優化洞見</text>
      </g>
    </g>
  </g>

  <!-- 核心價值總結 -->
  <g transform="translate(50, 2170)">
    <rect width="1300" height="150" rx="20" fill="rgba(103, 126, 234, 0.15)" stroke="#667eea" stroke-width="2"/>
    <text x="650" y="35" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="24" font-weight="bold" fill="#2d3748">✨ 進階 AI 提示詞系統價值</text>
    
    <text x="50" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">🎯 策略性洞見：從評估指南的深層次理解中提取實戰性 AI SEO 優化建議</text>
    <text x="50" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">🔍 多維度分析：涵蓋品牌曝光、意圖對齊、競品分析、技術實作、趨勢洞察五大層面</text>
    <text x="50" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">🚀 實戰導向：每個提示詞都保留關鍵變數，可自行替換並直接應用於實際專案</text>
    
    <text x="650" y="145" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">目標：建立系統性 AI SEO 優化工作流程，提升品牌在 AI 搜尋時代的競爭力</text>
  </g>

  <!-- 頁尾公司資訊 -->
  <g transform="translate(0, 2350)">
    <rect width="1400" height="150" fill="url(#headerGradient)" filter="url(#shadow)"/>
    <rect x="20" y="20" width="1360" height="110" rx="15" fill="rgba(255,255,255,0.95)"/>
    
    <!-- 公司標誌 -->
    <circle cx="120" cy="75" r="40" fill="#667eea"/>
    <text x="120" y="65" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="white">SEO</text>
    <text x="120" y="85" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="white">優化王</text>
    
    <!-- 公司資訊 -->
    <text x="180" y="45" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" font-weight="bold" fill="#2d3748">柏瀚國際科技有限公司 SEOKING INTERNATIONAL TECHNOLOGY CO.</text>
    <text x="180" y="70" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">統一編號：27305928 ｜ 地址：104 台北市中山區新生北路二段31之1號9樓之7</text>
    <text x="180" y="95" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">電話：0928-111-458 ｜ Email：<EMAIL> ｜ 官網：https://www.seoking.com.tw</text>
    <text x="180" y="120" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">AISO 360™ 全方位 AI 搜尋優化策略框架 • 亞太區首選策略夥伴</text>
    
    <!-- AI 提示詞標誌 -->
    <rect x="1180" y="30" width="180" height="90" rx="10" fill="url(#promptGradient1)" filter="url(#shadow)"/>
    <text x="1270" y="55" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="18" font-weight="bold" fill="#2d3748">🚀 AI 提示詞</text>
    <text x="1270" y="80" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" fill="#4a5568">進階架構</text>
    <text x="1270" y="105" text-anchor="middle" font-family="'Microsoft JhengHei', 'Noto Sans TC', sans-serif" 
          font-size="16" font-weight="bold" fill="#667eea">實戰→優化→成長</text>
  </g>
</svg>